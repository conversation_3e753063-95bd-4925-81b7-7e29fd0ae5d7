# ✅ VPS DEPLOYMENT CHECKLIST

## 📋 PRE-DEPLOYMENT CHECKLIST

### On Your Laptop:
- [ ] Trading system is working and tested
- [ ] Best model identified (Cycle 73: 0.8092 composite score)
- [ ] Binance API keys ready (with trading permissions)
- [ ] SSH access to VPS confirmed
- [ ] All deployment files created

### VPS Requirements:
- [ ] Ubuntu 24.04 VPS accessible
- [ ] IP: *********** reachable
- [ ] Root or sudo access available
- [ ] Minimum 2GB RAM, 20GB storage
- [ ] Stable internet connection

---

## 🚀 DEPLOYMENT STEPS CHECKLIST

### Phase 1: Laptop Preparation
- [ ] Run `python vps_deployment/deploy_to_vps.py`
- [ ] Verify all files created in `vps_deployment/`
- [ ] Model exported: `best_model_cycle73.pth`
- [ ] Requirements file created
- [ ] Environment template ready

### Phase 2: VPS Initial Setup
- [ ] Connect to VPS: `ssh root@***********`
- [ ] Create trading user: `sudo useradd -m trading`
- [ ] Set trading user password
- [ ] Add trading user to sudo group
- [ ] Switch to trading user

### Phase 3: System Configuration
- [ ] Update system packages: `sudo apt update && upgrade`
- [ ] Install Python 3.11 and dependencies
- [ ] Create project directory: `/home/<USER>/live_trading`
- [ ] Create subdirectories: `src`, `models`, `logs`, `config`
- [ ] Setup Python virtual environment

### Phase 4: File Transfer
- [ ] Transfer `live_trading_main.py`
- [ ] Transfer `vps_monitor.py`
- [ ] Transfer `requirements.txt`
- [ ] Transfer `.env.template`
- [ ] Transfer model file to `models/`
- [ ] Transfer source code to `src/`
- [ ] Transfer config files

### Phase 5: Environment Setup
- [ ] Install Python dependencies: `pip install -r requirements.txt`
- [ ] Copy `.env.template` to `.env`
- [ ] Edit `.env` with real API keys
- [ ] Set file permissions: `chmod 600 .env`
- [ ] Make scripts executable: `chmod +x *.py`

### Phase 6: Testing
- [ ] Test model loading
- [ ] Test API connection
- [ ] Run trading system manually
- [ ] Verify log output
- [ ] Test graceful shutdown (Ctrl+C)

### Phase 7: Production Setup
- [ ] Install supervisor: `sudo apt install supervisor`
- [ ] Copy supervisor config
- [ ] Reload supervisor configuration
- [ ] Start trading service
- [ ] Verify service is running
- [ ] Setup monitoring script

### Phase 8: Security & Monitoring
- [ ] Configure firewall: `sudo ufw enable`
- [ ] Disable unnecessary services
- [ ] Setup log rotation
- [ ] Configure monitoring alerts
- [ ] Test auto-restart functionality

---

## 🔍 VERIFICATION CHECKLIST

### System Health:
- [ ] Trading process running: `sudo supervisorctl status`
- [ ] Logs being generated: `tail -f logs/trading.log`
- [ ] No error messages in logs
- [ ] System resources normal: `htop`
- [ ] Disk space sufficient: `df -h`

### Trading Functionality:
- [ ] Model predictions appearing in logs
- [ ] Market data being fetched successfully
- [ ] Trading decisions being made
- [ ] Balance updates being logged
- [ ] Commission calculations working

### Monitoring:
- [ ] Monitor script running
- [ ] System metrics being collected
- [ ] Alert system configured (if enabled)
- [ ] Log files rotating properly
- [ ] Remote monitoring from laptop working

---

## 📊 SUCCESS CRITERIA

### ✅ Deployment is successful when:

1. **Process Management:**
   ```bash
   sudo supervisorctl status live_trading
   # Output: live_trading RUNNING pid 1234, uptime 0:05:00
   ```

2. **Regular Log Activity:**
   ```bash
   tail -f logs/trading.log
   # Should show STATUS updates every hour
   ```

3. **Model Predictions:**
   ```bash
   grep "Model decision" logs/trading.log | tail -5
   # Should show recent action predictions
   ```

4. **API Connectivity:**
   ```bash
   grep "Data collected successfully" logs/trading.log | tail -1
   # Should show recent successful data fetch
   ```

5. **System Resources:**
   ```bash
   free -h && df -h
   # Memory usage < 80%, Disk usage < 90%
   ```

---

## 🚨 TROUBLESHOOTING CHECKLIST

### If Trading Process Won't Start:
- [ ] Check supervisor logs: `sudo tail /var/log/supervisor/supervisord.log`
- [ ] Check error logs: `tail logs/error.log`
- [ ] Verify Python path in supervisor config
- [ ] Check file permissions
- [ ] Verify virtual environment activation

### If Model Loading Fails:
- [ ] Check model file exists: `ls -la models/`
- [ ] Verify model file size (should be > 1MB)
- [ ] Test model loading manually
- [ ] Check Python dependencies installed
- [ ] Verify PyTorch installation

### If API Errors Occur:
- [ ] Check API keys in `.env` file
- [ ] Verify API key permissions on Binance
- [ ] Test API connection manually
- [ ] Check internet connectivity
- [ ] Verify Binance API status

### If No Trading Activity:
- [ ] Check if market is open
- [ ] Verify model is making predictions
- [ ] Check risk management settings
- [ ] Review trading logic
- [ ] Check balance and position limits

---

## 📞 SUPPORT COMMANDS

### Quick Diagnostics:
```bash
# Full system check
ssh trading@*********** "
echo '=== SYSTEM STATUS ==='
uptime
free -h
df -h
echo '=== TRADING STATUS ==='
sudo supervisorctl status live_trading
echo '=== RECENT LOGS ==='
tail -n 5 /home/<USER>/live_trading/logs/trading.log
echo '=== PROCESS INFO ==='
ps aux | grep live_trading_main
"
```

### Emergency Stop:
```bash
# Stop all trading immediately
ssh trading@*********** "sudo supervisorctl stop live_trading"
```

### Emergency Restart:
```bash
# Restart trading system
ssh trading@*********** "sudo supervisorctl restart live_trading"
```

---

## 🎯 POST-DEPLOYMENT TASKS

### Daily Tasks:
- [ ] Check trading performance logs
- [ ] Monitor system resource usage
- [ ] Verify API rate limits not exceeded
- [ ] Review any error messages
- [ ] Check account balance progression

### Weekly Tasks:
- [ ] Review trading performance metrics
- [ ] Update system packages if needed
- [ ] Rotate log files if large
- [ ] Backup trading configuration
- [ ] Test monitoring alerts

### Monthly Tasks:
- [ ] Evaluate model performance
- [ ] Consider model updates from laptop
- [ ] Review and optimize system resources
- [ ] Update security configurations
- [ ] Analyze trading statistics

---

## 🎉 DEPLOYMENT COMPLETE!

**When all checkboxes are ✅, your live trading system is successfully deployed and running 24/7 on the VPS!**

**Key Benefits Achieved:**
- 🚀 **Automated Trading**: TCN-CNN-PPO model running live
- 🔄 **24/7 Operation**: Continuous trading without laptop
- 📊 **Real-time Data**: Direct Binance API integration
- 🛡️ **Risk Management**: 5% risk model with stop losses
- 📝 **Full Logging**: Complete audit trail
- 🔧 **Auto-restart**: Resilient to crashes
- 📱 **Remote Monitoring**: Control from anywhere

**Your laptop is now free for development while the VPS handles live trading!**
