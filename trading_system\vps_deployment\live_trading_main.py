#!/usr/bin/env python3
"""
Live Trading Main Engine for VPS Deployment
TCN-CNN-PPO Algorithm Live Trading System
"""
import asyncio
import logging
import os
import sys
import signal
import json
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional
import torch
import torch.nn as nn
from dataclasses import dataclass

# Add src to path
sys.path.append(str(Path(__file__).parent / 'src'))

from trading.binance_data_fetcher import BinanceDataFetcher
from trading.grid_trading_env import GridTradingEnv
from utils.trading_feature_extractor import TradingFeatureExtractor


@dataclass
class TradingConfig:
    """Trading configuration parameters."""
    initial_capital: float = 300.0
    risk_per_trade: float = 0.05
    grid_spacing: float = 0.0025
    take_profit_multiplier: float = 2.0
    fee_rate: float = 0.001
    symbol: str = "BTCUSDT"
    timeframe: str = "1h"
    model_path: str = "models/best_model_cycle73.pth"
    max_positions: int = 5
    stop_loss_pct: float = 0.05


class SimpleTCNPPO(nn.Module):
    """Simplified TCN-PPO model for live trading."""
    
    def __init__(self, input_size: int = 216, hidden_size: int = 128, num_actions: int = 3):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_actions = num_actions
        
        # Feature processing layers
        self.feature_net = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Policy head (action probabilities)
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, num_actions),
            nn.Softmax(dim=-1)
        )
        
        # Value head (state value estimation)
        self.value_head = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1)
        )
    
    def forward(self, x):
        """Forward pass through the network."""
        features = self.feature_net(x)
        policy = self.policy_head(features)
        value = self.value_head(features)
        return policy, value


class LiveTradingEngine:
    """Main live trading engine for VPS deployment."""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.running = False
        self.model = None
        self.feature_extractor = None
        self.env = None
        self.data_fetcher = None
        
        # Trading state
        self.current_balance = config.initial_capital
        self.positions = []
        self.trade_history = []
        self.last_action_time = None
        
        # Setup logging
        self.setup_logging()
        
        # Load environment variables
        self.load_environment()
        
        self.logger.info("Live Trading Engine initialized")
    
    def setup_logging(self):
        """Setup comprehensive logging."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'trading.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('LiveTrading')
        self.logger.info("Logging system initialized")
    
    def load_environment(self):
        """Load environment variables and API keys."""
        from dotenv import load_dotenv
        load_dotenv()
        
        # Validate required environment variables
        required_vars = ['BINANCE_API_KEY', 'BINANCE_SECRET_KEY']
        for var in required_vars:
            if not os.getenv(var):
                raise ValueError(f"Missing required environment variable: {var}")
        
        self.logger.info("Environment variables loaded successfully")
    
    async def initialize(self):
        """Initialize all components for live trading."""
        try:
            self.logger.info("Initializing live trading components...")
            
            # Initialize model
            await self.load_model()
            
            # Initialize feature extractor
            self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
            
            # Initialize trading environment
            self.env = GridTradingEnv(
                initial_balance=self.config.initial_capital,
                risk_per_trade=self.config.risk_per_trade,
                grid_spacing=self.config.grid_spacing,
                take_profit_multiplier=self.config.take_profit_multiplier,
                fee_rate=self.config.fee_rate
            )
            
            # Initialize data fetcher
            self.data_fetcher = BinanceDataFetcher()
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Initialization failed: {e}")
            raise
    
    async def load_model(self):
        """Load the trained model for live trading."""
        try:
            model_path = Path(self.config.model_path)
            if not model_path.exists():
                raise FileNotFoundError(f"Model file not found: {model_path}")
            
            # Initialize model
            self.model = SimpleTCNPPO(input_size=216, hidden_size=128, num_actions=3)
            
            # Load trained weights
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Handle different checkpoint formats
            if 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            
            # Set to evaluation mode
            self.model.eval()
            
            self.logger.info(f"Model loaded successfully from {model_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise
    
    async def get_market_data(self) -> List:
        """Fetch latest market data for decision making."""
        try:
            # Fetch recent candles for feature extraction
            end_time = datetime.now(timezone.utc)
            
            response = await self.data_fetcher.fetch_historical_data(
                symbol=self.config.symbol,
                interval=self.config.timeframe,
                limit=100  # Get enough data for feature extraction
            )
            
            if not response.success:
                raise RuntimeError(f"Failed to fetch market data: {response.error}")
            
            return response.data
            
        except Exception as e:
            self.logger.error(f"Failed to get market data: {e}")
            raise
    
    async def make_trading_decision(self, candles: List) -> int:
        """Make trading decision using the trained model."""
        try:
            # Extract features from market data
            features = await self.feature_extractor.extract_features(candles)
            
            # Convert to tensor
            feature_tensor = torch.FloatTensor(features).unsqueeze(0)
            
            # Get model prediction
            with torch.no_grad():
                policy, value = self.model(feature_tensor)
                action_probs = policy.squeeze(0)
                
                # Get action with highest probability
                action = torch.argmax(action_probs).item()
            
            # Log decision
            self.logger.info(f"Model decision: Action={action}, Probs={action_probs.tolist()}")
            
            return action
            
        except Exception as e:
            self.logger.error(f"Failed to make trading decision: {e}")
            return 2  # Default to HOLD
    
    async def execute_trade(self, action: int, current_price: float):
        """Execute trading action."""
        try:
            action_names = {0: "BUY", 1: "SELL", 2: "HOLD"}
            action_name = action_names.get(action, "UNKNOWN")
            
            self.logger.info(f"Executing action: {action_name} at price ${current_price:.2f}")
            
            if action == 0:  # BUY
                await self.execute_buy_order(current_price)
            elif action == 1:  # SELL
                await self.execute_sell_order(current_price)
            else:  # HOLD
                self.logger.info("Holding position - no action taken")
            
            # Update last action time
            self.last_action_time = datetime.now(timezone.utc)
            
        except Exception as e:
            self.logger.error(f"Failed to execute trade: {e}")
    
    async def execute_buy_order(self, price: float):
        """Execute buy order."""
        # Calculate position size based on risk management
        risk_amount = self.current_balance * self.config.risk_per_trade
        position_size = risk_amount / price
        
        # Log trade details
        trade_info = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'action': 'BUY',
            'price': price,
            'size': position_size,
            'risk_amount': risk_amount,
            'balance_before': self.current_balance
        }
        
        self.logger.info(f"BUY ORDER: {trade_info}")
        
        # In live trading, this would place actual order via Binance API
        # For now, simulate the trade
        self.simulate_trade(trade_info)
    
    async def execute_sell_order(self, price: float):
        """Execute sell order."""
        # Check if we have positions to sell
        if not self.positions:
            self.logger.info("No positions to sell")
            return
        
        # For simplicity, close the oldest position
        position = self.positions[0]
        
        trade_info = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'action': 'SELL',
            'price': price,
            'size': position['size'],
            'entry_price': position['price'],
            'balance_before': self.current_balance
        }
        
        self.logger.info(f"SELL ORDER: {trade_info}")
        
        # Simulate the trade
        self.simulate_trade(trade_info)
    
    def simulate_trade(self, trade_info: Dict):
        """Simulate trade execution for testing."""
        # This is a simplified simulation
        # In live trading, replace with actual Binance API calls
        
        if trade_info['action'] == 'BUY':
            # Add position
            position = {
                'timestamp': trade_info['timestamp'],
                'price': trade_info['price'],
                'size': trade_info['size'],
                'type': 'LONG'
            }
            self.positions.append(position)
            
            # Update balance (subtract cost + fees)
            cost = trade_info['price'] * trade_info['size']
            fee = cost * self.config.fee_rate
            self.current_balance -= (cost + fee)
            
        elif trade_info['action'] == 'SELL' and self.positions:
            # Remove position
            position = self.positions.pop(0)
            
            # Calculate P&L
            revenue = trade_info['price'] * trade_info['size']
            cost = position['price'] * position['size']
            fee = revenue * self.config.fee_rate
            pnl = revenue - cost - fee
            
            # Update balance
            self.current_balance += revenue - fee
            
            trade_info['pnl'] = pnl
            trade_info['return_pct'] = (pnl / cost) * 100
        
        # Add to trade history
        self.trade_history.append(trade_info)
        
        # Log updated balance
        self.logger.info(f"Updated balance: ${self.current_balance:.2f}")
    
    async def run_trading_loop(self):
        """Main trading loop."""
        self.logger.info("Starting live trading loop...")
        self.running = True
        
        while self.running:
            try:
                # Get latest market data
                candles = await self.get_market_data()
                current_price = candles[-1].close
                
                # Make trading decision
                action = await self.make_trading_decision(candles)
                
                # Execute trade if needed
                await self.execute_trade(action, current_price)
                
                # Log current status
                self.log_status(current_price)
                
                # Wait before next iteration (1 hour for 1h timeframe)
                await asyncio.sleep(3600)  # 1 hour
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry
    
    def log_status(self, current_price: float):
        """Log current trading status."""
        status = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'current_price': current_price,
            'balance': self.current_balance,
            'positions': len(self.positions),
            'total_trades': len(self.trade_history),
            'uptime': datetime.now(timezone.utc).isoformat()
        }
        
        self.logger.info(f"STATUS: {status}")
    
    def shutdown(self):
        """Graceful shutdown."""
        self.logger.info("Shutting down live trading engine...")
        self.running = False
        
        # Save final state
        final_state = {
            'shutdown_time': datetime.now(timezone.utc).isoformat(),
            'final_balance': self.current_balance,
            'total_trades': len(self.trade_history),
            'positions': self.positions,
            'trade_history': self.trade_history[-10:]  # Last 10 trades
        }
        
        # Save to file
        with open('logs/final_state.json', 'w') as f:
            json.dump(final_state, f, indent=2)
        
        self.logger.info("Shutdown complete")


async def main():
    """Main entry point."""
    # Load configuration
    config = TradingConfig()
    
    # Initialize trading engine
    engine = LiveTradingEngine(config)
    
    # Setup signal handlers for graceful shutdown
    def signal_handler(signum, frame):
        print(f"\nReceived signal {signum}, shutting down...")
        engine.shutdown()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize components
        await engine.initialize()
        
        # Start trading loop
        await engine.run_trading_loop()
        
    except KeyboardInterrupt:
        print("\nKeyboard interrupt received")
    except Exception as e:
        print(f"Fatal error: {e}")
    finally:
        engine.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
