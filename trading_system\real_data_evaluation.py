"""
Real Data Only Evaluation System
NO DUMMY DATA - Only real market data from Binance
Focus on best performance models with detailed trade analysis
"""
import asyncio
import sys
import json
import torch
import numpy as np
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List
import logging

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.ml.integrated_training import IntegratedTrainingPipeline, CompositeMetricsCalculator
from real_data_html_generator import RealDataHTMLGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RealDataEvaluator:
    """Real data only evaluator - NO DUMMY DATA ALLOWED."""

    def __init__(self, initial_capital: float = 300.0):
        self.initial_capital = initial_capital
        self.training_days = 60
        self.test_days = 30

        # Model saving
        self.best_model = None
        self.best_composite_score = 0.0
        self.best_cycle_data = None

        logger.info(f"Real Data Evaluator initialized:")
        logger.info(f"  Training: {self.training_days} days")
        logger.info(f"  Testing: {self.test_days} days")
        logger.info(f"  Initial capital: ${self.initial_capital}")
        logger.info("  NO DUMMY DATA - Real market data only")

    async def collect_real_market_data(self) -> Dict:
        """Collect real market data from Binance - NO DUMMY DATA."""
        logger.info("Collecting REAL market data from Binance...")

        total_days = self.training_days + self.test_days + 5  # Small buffer

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=total_days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch REAL data: {response.error}")

            all_candles = response.data

            # Split real data: 60 days training + 30 days testing
            train_hours = self.training_days * 24
            test_hours = self.test_days * 24

            training_data = all_candles[:train_hours]
            test_data = all_candles[train_hours:train_hours + test_hours]

            logger.info(f"REAL data collected:")
            logger.info(f"  Training: {len(training_data)} candles ({self.training_days} days)")
            logger.info(f"  Testing: {len(test_data)} candles ({self.test_days} days)")
            logger.info(f"  Date range: {training_data[0].timestamp} to {test_data[-1].timestamp}")

            return {
                'training': training_data,
                'testing': test_data
            }

    async def run_single_cycle_real_data(self, cycle_num: int, data_splits: Dict) -> Dict:
        """Run single cycle with REAL data only - NO DUMMY DATA."""
        try:
            logger.info(f"Cycle {cycle_num}: Starting with REAL market data")

            # Initialize fresh pipeline
            pipeline = IntegratedTrainingPipeline(initial_balance=self.initial_capital)

            # Training phase with REAL data
            logger.info(f"Cycle {cycle_num}: Training on REAL data...")
            training_episodes = 15  # Focused training

            best_training_score = 0
            for episode in range(training_episodes):
                episode_data = await pipeline.simulate_episode_detailed(data_splits['training'], pipeline.model)

                if episode_data['composite_score'] > best_training_score:
                    best_training_score = episode_data['composite_score']

                # Training update
                if len(episode_data['states']) > 0:
                    await pipeline.quick_training_update(episode_data)

            # Out-of-sample testing with REAL data
            logger.info(f"Cycle {cycle_num}: Testing on REAL out-of-sample data...")
            test_episode = await pipeline.simulate_episode_detailed(data_splits['testing'], pipeline.model)

            # Check if this is the best model so far
            test_composite_score = test_episode['composite_score']
            if test_composite_score > self.best_composite_score:
                self.best_composite_score = test_composite_score
                self.best_model = pipeline.model.state_dict().copy()
                self.best_cycle_data = {
                    'cycle': cycle_num,
                    'composite_score': test_composite_score,
                    'test_episode': test_episode
                }
                logger.info(f"🏆 NEW BEST MODEL! Cycle {cycle_num}: Score {test_composite_score:.4f}")

                # Save best model
                models_dir = Path("models")
                models_dir.mkdir(exist_ok=True)
                torch.save(self.best_model, f"models/best_model_cycle_{cycle_num}_score_{test_composite_score:.4f}.pth")

            # Calculate drawdown curve
            drawdown_curve = self.calculate_drawdown_curve(test_episode['equity_curve'])

            cycle_result = {
                'cycle': cycle_num,
                'training': {
                    'best_composite_score': best_training_score,
                    'episodes_trained': training_episodes
                },
                'testing': {
                    'composite_score': test_composite_score,
                    'composite_metrics': test_episode['composite_metrics'],
                    'final_balance': test_episode['final_balance'],
                    'total_return': test_episode['total_return'],
                    'total_trades': len(test_episode['detailed_trades']),
                    'equity_curve': test_episode['equity_curve'],
                    'drawdown_curve': drawdown_curve,
                    'detailed_trades': test_episode['detailed_trades'],
                    'commission_paid': test_episode.get('commission_paid', 0),
                    'max_drawdown': max(drawdown_curve) if drawdown_curve else 0,
                    'winning_trades': len([t for t in test_episode['detailed_trades'] if t.get('pnl_net', 0) > 0]),
                    'losing_trades': len([t for t in test_episode['detailed_trades'] if t.get('pnl_net', 0) < 0])
                }
            }

            logger.info(f"Cycle {cycle_num}: Score={test_composite_score:.4f}, "
                       f"Return={test_episode['total_return']:+.2f}%, "
                       f"Trades={len(test_episode['detailed_trades'])}")

            return cycle_result

        except Exception as e:
            logger.error(f"Cycle {cycle_num} failed: {e}")
            return {
                'cycle': cycle_num,
                'error': str(e),
                'training': {'best_composite_score': 0, 'episodes_trained': 0},
                'testing': {'composite_score': 0, 'final_balance': self.initial_capital, 'total_return': 0, 'total_trades': 0}
            }

    def calculate_drawdown_curve(self, equity_curve: List[float]) -> List[float]:
        """Calculate drawdown curve from equity curve."""
        if not equity_curve or len(equity_curve) < 2:
            return []

        drawdowns = []
        peak = equity_curve[0]

        for equity in equity_curve:
            if equity > peak:
                peak = equity
            drawdown = (peak - equity) / peak
            drawdowns.append(drawdown)

        return drawdowns

    async def run_short_evaluation(self, cycles: int = 10) -> List[Dict]:
        """Run short evaluation with REAL data only."""
        logger.info(f"🚀 STARTING {cycles}-CYCLE REAL DATA EVALUATION")
        logger.info("=" * 60)
        logger.info("📊 NO DUMMY DATA - Real Binance market data only")
        logger.info("=" * 60)

        # Collect REAL market data
        data_splits = await self.collect_real_market_data()

        # Run cycles with REAL data
        results = []
        start_time = datetime.now()

        for cycle in range(cycles):
            cycle_result = await self.run_single_cycle_real_data(cycle, data_splits)
            results.append(cycle_result)

            # Progress update
            if cycle % 5 == 0 and cycle > 0:
                elapsed = datetime.now() - start_time
                logger.info(f"Progress: {cycle}/{cycles} cycles completed in {elapsed}")

        logger.info(f"✅ {cycles}-cycle REAL data evaluation completed!")
        return results

    def analyze_real_results(self, results: List[Dict]) -> Dict:
        """Analyze results from REAL data only."""
        logger.info("📊 Analyzing REAL data results...")

        # Filter valid results
        valid_results = [r for r in results if 'error' not in r]
        failed_cycles = len(results) - len(valid_results)

        if not valid_results:
            return {'error': 'No valid results from REAL data'}

        # Extract metrics from REAL data
        composite_scores = [r['testing']['composite_score'] for r in valid_results]
        returns = [r['testing']['total_return'] for r in valid_results]
        trades = [r['testing']['total_trades'] for r in valid_results]

        # Best performing cycles (REAL data only)
        best_cycles = sorted(valid_results, key=lambda x: x['testing']['composite_score'], reverse=True)

        analysis = {
            'summary': {
                'total_cycles': len(results),
                'valid_cycles': len(valid_results),
                'failed_cycles': failed_cycles,
                'success_rate': len(valid_results) / len(results) * 100,
                'data_source': 'REAL_BINANCE_DATA_ONLY'
            },
            'performance': {
                'best_composite_score': max(composite_scores),
                'avg_composite_score': np.mean(composite_scores),
                'best_return': max(returns),
                'avg_return': np.mean(returns),
                'profitable_cycles': len([r for r in returns if r > 0])
            },
            'best_cycles': best_cycles[:5],  # Top 5 from REAL data
            'best_model_info': {
                'cycle': self.best_cycle_data['cycle'] if self.best_cycle_data else None,
                'composite_score': self.best_composite_score,
                'model_saved': self.best_model is not None
            }
        }

        return analysis


async def main():
    """Main evaluation function - REAL DATA ONLY."""
    evaluator = RealDataEvaluator(initial_capital=300.0)

    # Run short evaluation with REAL data
    results = await evaluator.run_short_evaluation(cycles=10)

    # Analyze REAL data results
    analysis = evaluator.analyze_real_results(results)

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save REAL data results
    results_file = f"reports/real_data_evaluation_{timestamp}.json"
    Path(results_file).parent.mkdir(parents=True, exist_ok=True)

    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    # Generate HTML report
    html_generator = RealDataHTMLGenerator()
    html_report = html_generator.generate_best_performance_report(results, analysis, timestamp)
    html_file = f"reports/real_data_evaluation_{timestamp}.html"

    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_report)

    # Print summary
    print("\n🎉 REAL DATA EVALUATION COMPLETED!")
    print("=" * 50)
    print(f"📊 Results saved to: {results_file}")
    print(f"📄 HTML Report saved to: {html_file}")
    print(f"📊 Data source: REAL Binance market data only")

    if 'error' not in analysis:
        print(f"\n📈 REAL DATA RESULTS:")
        print(f"   Valid cycles: {analysis['summary']['valid_cycles']}")
        print(f"   Best composite score: {analysis['performance']['best_composite_score']:.4f}")
        print(f"   Best return: {analysis['performance']['best_return']:+.2f}%")
        print(f"   Profitable cycles: {analysis['performance']['profitable_cycles']}")

        if analysis['best_model_info']['model_saved']:
            print(f"\n🏆 BEST MODEL SAVED:")
            print(f"   Cycle: {analysis['best_model_info']['cycle']}")
            print(f"   Score: {analysis['best_model_info']['composite_score']:.4f}")
            print(f"   Location: models/best_model_cycle_*.pth")

    logger.info("REAL data evaluation completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
