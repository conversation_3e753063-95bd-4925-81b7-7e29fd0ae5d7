"""
Grid Trading Environment

This module implements the trading environment for the grid trading strategy.
It manages the state, actions, and rewards for the reinforcement learning agent.
"""
from dataclasses import dataclass, field
from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Union
import numpy as np
import pandas as pd
from datetime import datetime, timezone

from ..data.models import OHLCVSchema
from ..config.settings import settings

class Action(Enum):
    """Available trading actions."""
    HOLD = 0
    BUY = 1
    SELL = 2

@dataclass
class Position:
    """Represents an open trading position."""
    entry_price: float
    entry_time: datetime
    position_type: str  # 'long' or 'short'
    size: float
    stop_loss: float
    take_profit: float
    current_price: float = 0.0
    pnl: float = 0.0
    pnl_pct: float = 0.0

    @property
    def direction(self):
        """Return direction as enum-like object for compatibility."""
        class Direction:
            def __init__(self, name):
                self.name = name
        return Direction('LONG' if self.position_type == 'long' else 'SHORT')

    def update(self, current_price: float) -> None:
        """Update position with current market price."""
        self.current_price = current_price
        if self.position_type == 'long':
            self.pnl = (current_price - self.entry_price) * self.size
        else:  # short
            self.pnl = (self.entry_price - current_price) * self.size
        self.pnl_pct = (self.pnl / (self.entry_price * self.size)) * 100

    def is_stopped_out(self) -> bool:
        """Check if position has hit stop loss or take profit."""
        if self.position_type == 'long':
            return (self.current_price <= self.stop_loss or
                   self.current_price >= self.take_profit)
        else:  # short
            return (self.current_price >= self.stop_loss or
                   self.current_price <= self.take_profit)

@dataclass
class GridLevel:
    """Represents a price level in the trading grid."""
    price: float
    is_active: bool = False
    last_touched: Optional[datetime] = None

class GridTradingEnv:
    """Grid Trading Environment for reinforcement learning."""

    def __init__(
        self,
        initial_balance: float = 10000.0,
        risk_per_trade: float = 0.05,
        grid_spacing: float = 0.0025,  # 0.25%
        take_profit_multiplier: float = 2.0,
        max_grid_levels: int = 100,
        position_ttl: int = 24 * 7,  # 1 week in hours
        fee_rate: float = 0.001  # 0.1% trading fee
    ):
        self.initial_balance = initial_balance
        self.balance = initial_balance
        self.equity = initial_balance
        self.risk_per_trade = risk_per_trade
        self.grid_spacing = grid_spacing
        self.take_profit_multiplier = take_profit_multiplier
        self.max_grid_levels = max_grid_levels
        self.position_ttl = position_ttl
        self.fee_rate = fee_rate

        # State
        self.current_price = 0.0
        self.current_time = None
        self.positions: List[Position] = []
        self.closed_positions: List[Position] = []
        self.grid_levels: List[GridLevel] = []
        self.trade_count = 0
        self.win_count = 0

        # Track performance
        self.equity_curve = []
        self.max_drawdown = 0.0
        self.max_equity = initial_balance

    def reset(self, initial_price: float, timestamp: datetime) -> np.ndarray:
        """Reset the environment to initial state."""
        self.balance = self.initial_balance
        self.equity = self.initial_balance
        self.current_price = initial_price
        self.current_time = timestamp
        self.positions = []
        self.closed_positions = []
        self.trade_count = 0
        self.win_count = 0
        self.equity_curve = [(timestamp, self.equity)]
        self.max_equity = self.initial_balance
        self.max_drawdown = 0.0

        # Initialize grid levels
        self._initialize_grid(initial_price)

        return self._get_state()

    def _initialize_grid(self, current_price: float) -> None:
        """Initialize grid levels around the current price."""
        self.grid_levels = []

        # Calculate number of levels above and below
        num_levels = min(50, self.max_grid_levels // 2)  # Start with 50 levels each side

        # Add levels below current price
        for i in range(1, num_levels + 1):
            price = current_price * (1 - self.grid_spacing * i)
            self.grid_levels.append(GridLevel(price=price))

        # Add current level
        self.grid_levels.append(GridLevel(price=current_price, is_active=True))

        # Add levels above current price
        for i in range(1, num_levels + 1):
            price = current_price * (1 + self.grid_spacing * i)
            self.grid_levels.append(GridLevel(price=price))

        # Sort by price
        self.grid_levels.sort(key=lambda x: x.price)

    def step(
        self,
        action: Action,
        current_price: float,
        timestamp: datetime,
        current_ohlcv: Optional[OHLCVSchema] = None
    ) -> Tuple[np.ndarray, float, bool, Dict]:
        """
        Execute one time step within the environment.

        Args:
            action: The action to take (HOLD, BUY, SELL)
            current_price: Current market price
            timestamp: Current timestamp
            current_ohlcv: Current OHLCV data (optional)

        Returns:
            tuple: (next_state, reward, done, info)
        """
        self.current_price = current_price
        self.current_time = timestamp

        # Update existing positions
        self._update_positions()

        # Execute action if no open positions
        if not self.positions:
            if action == Action.BUY:
                self._open_position('long', current_price, timestamp)
            elif action == Action.SELL:
                self._open_position('short', current_price, timestamp)

        # Update grid levels
        self._update_grid_levels()

        # Calculate reward
        reward = self._calculate_reward()

        # Check if done (end of episode)
        done = self._is_done()

        # Update equity curve (balance + unrealized P&L from open positions + value of open positions)
        open_position_value = sum(pos.entry_price * pos.size for pos in self.positions)
        unrealized_pnl = sum(pos.pnl for pos in self.positions)
        self.equity = self.balance + open_position_value + unrealized_pnl
        self.equity_curve.append((timestamp, self.equity))

        # Update max drawdown
        self.max_equity = max(self.max_equity, self.equity)
        drawdown = (self.max_equity - self.equity) / self.max_equity
        self.max_drawdown = max(self.max_drawdown, drawdown)

        # Prepare info dict
        info = {
            'balance': self.balance,
            'equity': self.equity,
            'open_positions': len(self.positions),
            'closed_positions': len(self.closed_positions),
            'win_rate': self.win_count / max(1, self.trade_count),
            'max_drawdown': self.max_drawdown,
            'current_price': current_price,
            'timestamp': timestamp
        }

        return self._get_state(), reward, done, info

    def _open_position(self, position_type: str, entry_price: float, timestamp: datetime) -> None:
        """Open a new trading position."""
        # Calculate position size based on risk
        stop_loss_pct = self.grid_spacing / 2
        take_profit_pct = stop_loss_pct * self.take_profit_multiplier

        if position_type == 'long':
            stop_loss = entry_price * (1 - stop_loss_pct)
            take_profit = entry_price * (1 + take_profit_pct)
        else:  # short
            stop_loss = entry_price * (1 + stop_loss_pct)
            take_profit = entry_price * (1 - take_profit_pct)

        # Calculate position size based on risk management
        risk_amount = self.balance * self.risk_per_trade
        stop_loss_distance = abs(entry_price - stop_loss)
        stop_loss_pct = stop_loss_distance / entry_price

        # Position value should not exceed available balance
        max_position_value = self.balance * 0.9  # Use max 90% of balance

        # Calculate position value based on risk
        # If we risk 'risk_amount' and stop loss is 'stop_loss_pct', then position value is:
        position_value = min(risk_amount / stop_loss_pct, max_position_value)

        # Convert to number of shares/units
        position_size = position_value / entry_price

        # Create and add position
        position = Position(
            entry_price=entry_price,
            entry_time=timestamp,
            position_type=position_type,
            size=position_size,
            stop_loss=stop_loss,
            take_profit=take_profit,
            current_price=entry_price
        )

        # Deduct position cost from balance (position_value already in USD)
        self.balance -= position_value * (1 + self.fee_rate)
        self.positions.append(position)
        self.trade_count += 1

    def _update_positions(self) -> None:
        """Update all open positions and close if necessary."""
        positions_to_remove = []

        for i, position in enumerate(self.positions):
            # Update position with current price
            position.update(self.current_price)

            # Check if position should be closed
            if position.is_stopped_out():
                # Calculate P&L
                if position.pnl > 0:
                    self.win_count += 1

                # Add to closed positions
                self.closed_positions.append(position)
                positions_to_remove.append(i)

                # Update balance (return the position value plus P&L)
                position_value = position.entry_price * position.size
                self.balance += position_value + position.pnl

        # Remove closed positions (in reverse order to avoid index issues)
        for i in sorted(positions_to_remove, reverse=True):
            if i < len(self.positions):
                self.positions.pop(i)

    def _update_grid_levels(self) -> None:
        """Update grid levels based on current price."""
        # Mark current level as active
        current_level = min(
            self.grid_levels,
            key=lambda x: abs(x.price - self.current_price)
        )
        current_level.is_active = True
        current_level.last_touched = self.current_time

        # Add new levels if price moves outside current grid
        if self.current_price < self.grid_levels[0].price:
            # Add levels below
            new_price = self.grid_levels[0].price * (1 - self.grid_spacing)
            self.grid_levels.insert(0, GridLevel(price=new_price, is_active=True, last_touched=self.current_time))

        elif self.current_price > self.grid_levels[-1].price:
            # Add levels above
            new_price = self.grid_levels[-1].price * (1 + self.grid_spacing)
            self.grid_levels.append(GridLevel(price=new_price, is_active=True, last_touched=self.current_time))

        # Trim grid if it gets too large
        if len(self.grid_levels) > self.max_grid_levels:
            # Remove oldest untouched levels
            min_datetime = datetime.min.replace(tzinfo=timezone.utc)
            self.grid_levels.sort(key=lambda x: x.last_touched or min_datetime)
            self.grid_levels = self.grid_levels[-self.max_grid_levels:]

            # Re-sort by price
            self.grid_levels.sort(key=lambda x: x.price)

    def _calculate_reward(self) -> float:
        """Calculate reward for the current step."""
        if not self.positions:
            return 0.0

        # Simple reward: P&L of all open positions
        total_pnl = sum(pos.pnl for pos in self.positions)
        return float(total_pnl / self.initial_balance)  # Return as % of initial balance

    def _is_done(self) -> bool:
        """Check if episode is done."""
        # Example: Stop if drawdown exceeds 20%
        return self.max_drawdown >= 0.2 or self.equity <= self.initial_balance * 0.5

    def _get_state(self) -> np.ndarray:
        """Get current state as numpy array."""
        # Simple state representation: price changes and position info
        state = [
            self.current_price,
            len(self.positions),
            self.balance / self.initial_balance,  # Normalized balance
            self.equity / self.initial_balance,   # Normalized equity
            self.max_drawdown,
            len(self.closed_positions) / max(1, self.trade_count),  # Trade frequency
        ]

        # Add position info if any
        if self.positions:
            avg_pnl = sum(pos.pnl_pct for pos in self.positions) / len(self.positions)
            state.extend([avg_pnl])
        else:
            state.extend([0.0])

        return np.array(state, dtype=np.float32)

    def get_metrics(self) -> Dict[str, float]:
        """Get current trading metrics."""
        return {
            'balance': self.balance,
            'equity': self.equity,
            'return_pct': (self.equity / self.initial_balance - 1) * 100,
            'max_drawdown': self.max_drawdown * 100,  # as percentage
            'trades': self.trade_count,
            'win_rate': (self.win_count / self.trade_count * 100) if self.trade_count > 0 else 0.0,
            'sharpe_ratio': self._calculate_sharpe_ratio()
        }

    def _calculate_sharpe_ratio(self, risk_free_rate: float = 0.0) -> float:
        """Calculate Sharpe ratio based on equity curve."""
        if len(self.equity_curve) < 2:
            return 0.0

        # Calculate daily returns
        returns = []
        for i in range(1, len(self.equity_curve)):
            prev_equity = self.equity_curve[i-1][1]
            curr_equity = self.equity_curve[i][1]
            returns.append((curr_equity - prev_equity) / prev_equity)

        if not returns:
            return 0.0

        # Annualize returns (assuming 365 trading days)
        returns = np.array(returns)
        excess_returns = returns - risk_free_rate / 365

        # Handle division by zero
        if np.std(returns) == 0:
            return 0.0

        return np.sqrt(365) * np.mean(excess_returns) / np.std(returns)
