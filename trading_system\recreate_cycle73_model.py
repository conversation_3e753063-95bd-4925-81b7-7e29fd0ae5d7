#!/usr/bin/env python3
"""
Recreate and Save Cycle 73 Model for VPS Deployment
Since Cycle 73 wasn't saved during the 20,000-cycle evaluation, we need to recreate it
"""
import asyncio
import sys
import torch
import json
from datetime import datetime
from pathlib import Path
from typing import Dict

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from comprehensive_1000_cycle_evaluation import Comprehensive1000CycleEvaluator


class Cycle73ModelRecreator:
    """Recreate the exact Cycle 73 model for VPS deployment."""

    def __init__(self):
        self.evaluator = Comprehensive1000CycleEvaluator(initial_capital=300.0)
        
    async def recreate_cycle73_model(self) -> str:
        """Recreate Cycle 73 model and save it for VPS deployment."""
        print("🔄 RECREATING CYCLE 73 MODEL FOR VPS DEPLOYMENT")
        print("=" * 60)
        print("📊 Target: Cycle 73 with 0.8208 composite score")
        print("🎯 Purpose: VPS live trading deployment")
        print("=" * 60)
        
        try:
            # Collect the same data that was used in the evaluation
            print("\n📊 Collecting evaluation data...")
            data_splits = await self.evaluator.collect_evaluation_data()
            print(f"✅ Data collected: {len(data_splits['training'])} training samples")
            
            # Run Cycle 73 specifically
            print(f"\n🔄 Running Cycle 73...")
            cycle_result = await self.evaluator.run_single_cycle(73, data_splits)
            
            # Extract the trained model
            print(f"\n📦 Extracting trained model...")
            model_state = cycle_result.get('model_state_dict')
            
            if model_state is None:
                print("❌ Model state not found in cycle result")
                return None
            
            # Save the model for VPS deployment
            model_file = await self.save_model_for_vps(cycle_result, model_state)
            
            print(f"\n🎉 SUCCESS!")
            print(f"📄 Model saved: {model_file}")
            print(f"📊 Composite Score: {cycle_result['testing']['composite_score']:.4f}")
            print(f"📈 Total Return: {cycle_result['testing']['total_return']:+,.2f}%")
            print(f"💰 Final Balance: ${cycle_result['testing']['final_balance']:,.2f}")
            print(f"⚠️ Max Drawdown: {cycle_result['testing']['max_drawdown']:.2f}%")
            
            return model_file
            
        except Exception as e:
            print(f"❌ Error recreating Cycle 73 model: {e}")
            raise
    
    async def save_model_for_vps(self, cycle_result: Dict, model_state: Dict) -> str:
        """Save the model in the format needed for VPS deployment."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create models directory
        models_dir = Path("models")
        models_dir.mkdir(exist_ok=True)
        
        # Create VPS deployment directory
        vps_models_dir = Path("vps_deployment")
        vps_models_dir.mkdir(exist_ok=True)
        
        # Extract performance metrics
        testing_data = cycle_result.get('testing', {})
        composite_score = testing_data.get('composite_score', 0)
        total_return = testing_data.get('total_return', 0)
        final_balance = testing_data.get('final_balance', 300)
        max_drawdown = testing_data.get('max_drawdown', 0)
        
        # Save the PyTorch model file for VPS
        model_filename = f"best_model_cycle73_score_{composite_score:.4f}.pth"
        model_path = vps_models_dir / model_filename
        
        # Save just the model state dict (compatible with VPS loading)
        torch.save(model_state, model_path)
        
        # Also save a backup in models directory
        backup_path = models_dir / model_filename
        torch.save(model_state, backup_path)
        
        # Create comprehensive model info file
        model_info = {
            'model_info': {
                'cycle_number': 73,
                'composite_score': composite_score,
                'total_return': total_return,
                'final_balance': final_balance,
                'max_drawdown': max_drawdown,
                'created_timestamp': timestamp,
                'model_file': model_filename,
                'model_architecture': 'SimpleTCNPPO',
                'input_size': 216,
                'hidden_size': 128,
                'num_actions': 3
            },
            'trading_config': {
                'initial_capital': 300.0,
                'risk_per_trade': 0.05,
                'grid_spacing': 0.0025,
                'take_profit_multiplier': 2.0,
                'fee_rate': 0.001,
                'symbol': 'BTCUSDT',
                'timeframe': '1h'
            },
            'performance_metrics': testing_data,
            'vps_deployment': {
                'ready_for_deployment': True,
                'model_path': str(model_path),
                'deployment_instructions': 'Use this model file for VPS live trading deployment'
            }
        }
        
        # Save model info
        info_file = vps_models_dir / f"cycle73_model_info_{timestamp}.json"
        with open(info_file, 'w') as f:
            json.dump(model_info, f, indent=2, default=str)
        
        print(f"✅ Model saved: {model_path}")
        print(f"✅ Model info saved: {info_file}")
        print(f"✅ Backup saved: {backup_path}")
        
        return str(model_path)
    
    async def verify_model_loading(self, model_path: str) -> bool:
        """Verify that the saved model can be loaded correctly."""
        print(f"\n🔍 VERIFYING MODEL LOADING...")
        
        try:
            # Import the model class
            from src.ml.integrated_training import SimpleTCNPPO
            
            # Create model instance
            model = SimpleTCNPPO(input_size=216, hidden_size=128, num_actions=3)
            
            # Load the saved state
            model_state = torch.load(model_path, map_location='cpu')
            model.load_state_dict(model_state)
            
            # Set to evaluation mode
            model.eval()
            
            # Test with dummy input
            dummy_input = torch.randn(1, 216)
            with torch.no_grad():
                policy, value = model(dummy_input)
                
            print(f"✅ Model loaded successfully!")
            print(f"✅ Policy output shape: {policy.shape}")
            print(f"✅ Value output shape: {value.shape}")
            print(f"✅ Policy probabilities: {policy.squeeze().tolist()}")
            print(f"✅ State value: {value.item():.4f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Model verification failed: {e}")
            return False


async def main():
    """Main function to recreate Cycle 73 model."""
    print("🚀 CYCLE 73 MODEL RECREATION FOR VPS DEPLOYMENT")
    print("=" * 60)
    
    # Initialize recreator
    recreator = Cycle73ModelRecreator()
    
    try:
        # Recreate and save the model
        model_path = await recreator.recreate_cycle73_model()
        
        if model_path:
            # Verify the model can be loaded
            verification_success = await recreator.verify_model_loading(model_path)
            
            if verification_success:
                print(f"\n🎉 CYCLE 73 MODEL READY FOR VPS DEPLOYMENT!")
                print(f"📄 Model file: {model_path}")
                print(f"🚀 Ready to transfer to VPS for live trading")
                print(f"\n📋 NEXT STEPS:")
                print(f"1. Copy {model_path} to your VPS deployment package")
                print(f"2. Update the VPS deployment script to use this model")
                print(f"3. Deploy to VPS following the step-by-step guide")
            else:
                print(f"\n❌ Model verification failed - check the model file")
        else:
            print(f"\n❌ Failed to recreate Cycle 73 model")
            
    except Exception as e:
        print(f"\n❌ Error in model recreation: {e}")


if __name__ == "__main__":
    asyncio.run(main())
