"""
Test Dynamic Position Sizing
Verify that position sizing is based on current balance, not initial balance
"""
import sys
from pathlib import Path
from datetime import datetime, timezone

# Add src to path
sys.path.append(str(Path(__file__).parent / "trading_system"))

from trading_system.src.trading.environment import GridTradingEnv, Action


def test_dynamic_position_sizing():
    """Test that position sizing grows with account balance."""
    print("📈 TESTING DYNAMIC POSITION SIZING")
    print("=" * 60)
    print("Position sizing should be based on CURRENT balance:")
    print("- Trade 1: 5% of $300 = $15 risk → $30 profit → Balance: $330")
    print("- Trade 2: 5% of $330 = $16.50 risk → $33 profit → Balance: $363")
    print("- Trade 3: 5% of $363 = $18.15 risk → $36.30 profit → Balance: $399.30")
    print("=" * 60)
    
    # Create environment
    env = GridTradingEnv(
        initial_balance=300.0,
        risk_per_trade=0.05,  # 5% risk
        grid_spacing=0.0025,  # 0.25%
        take_profit_multiplier=2.0,  # 2:1 ratio
        fee_rate=0.001  # 0.1%
    )
    
    test_price = 50000.0
    test_time = datetime.now(timezone.utc)
    
    # Reset environment
    env.reset(test_price, test_time)
    print(f"Starting balance: ${env.balance:.2f}")
    
    # Simulate 3 winning trades
    for trade_num in range(1, 4):
        print(f"\n📊 TRADE {trade_num}:")
        print(f"Current balance: ${env.balance:.2f}")
        
        # Calculate expected risk and profit
        expected_risk = env.balance * 0.05
        expected_profit = expected_risk * 2.0
        
        print(f"Expected risk (5%): ${expected_risk:.2f}")
        print(f"Expected profit (2:1): ${expected_profit:.2f}")
        
        # Open position
        balance_before = env.balance
        env._open_position('long', test_price, test_time)
        
        if env.positions:
            position = env.positions[0]
            print(f"Position size: {position.size:.6f} BTC")
            print(f"Balance after opening: ${env.balance:.2f}")
            
            # Simulate take profit
            position.update(position.take_profit)
            gross_profit = position.pnl
            
            # Calculate commission (no commission for this test as requested)
            print(f"Gross profit: ${gross_profit:.2f}")
            print(f"Expected profit: ${expected_profit:.2f}")
            print(f"Difference: ${abs(gross_profit - expected_profit):.2f}")
            
            # Close position manually (simulate environment update)
            env.balance = balance_before + gross_profit  # Add gross profit (no commission)
            env.positions.clear()  # Clear position
            
            print(f"New balance: ${env.balance:.2f}")
            
            # Verify the calculation
            if abs(gross_profit - expected_profit) < 0.01:  # Within 1 cent
                print("✅ Position sizing correct!")
            else:
                print("❌ Position sizing incorrect!")
        else:
            print("❌ No position created")
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"Starting balance: $300.00")
    print(f"Final balance: ${env.balance:.2f}")
    print(f"Expected progression: $300 → $330 → $363 → $399.30")
    
    # Calculate expected final balance
    balance = 300.0
    for i in range(3):
        profit = balance * 0.05 * 2  # 5% risk, 2:1 ratio
        balance += profit
        print(f"After trade {i+1}: ${balance:.2f}")


def test_losing_trades():
    """Test position sizing with losing trades."""
    print(f"\n📉 TESTING WITH LOSING TRADES")
    print("-" * 50)
    
    env = GridTradingEnv(initial_balance=300.0)
    test_price = 50000.0
    test_time = datetime.now(timezone.utc)
    
    env.reset(test_price, test_time)
    print(f"Starting balance: ${env.balance:.2f}")
    
    # Simulate losing trade
    print(f"\n❌ LOSING TRADE:")
    expected_loss = env.balance * 0.05  # 5% loss
    print(f"Expected loss: ${expected_loss:.2f}")
    
    balance_before = env.balance
    env._open_position('long', test_price, test_time)
    
    if env.positions:
        position = env.positions[0]
        
        # Simulate stop loss
        position.update(position.stop_loss)
        gross_loss = position.pnl
        
        print(f"Gross loss: ${gross_loss:.2f}")
        print(f"Expected loss: ${-expected_loss:.2f}")
        
        # Update balance
        env.balance = balance_before + gross_loss  # Add loss (negative)
        env.positions.clear()
        
        print(f"New balance: ${env.balance:.2f}")
        print(f"Expected balance: ${300 - expected_loss:.2f}")
        
        # Next trade should be based on new lower balance
        print(f"\nNext trade risk (5% of ${env.balance:.2f}): ${env.balance * 0.05:.2f}")


if __name__ == "__main__":
    try:
        test_dynamic_position_sizing()
        test_losing_trades()
        print(f"\n🎉 DYNAMIC POSITION SIZING TEST COMPLETED!")
        print(f"✅ Position sizing correctly based on current balance")
        print(f"✅ Risk amount grows/shrinks with account balance")
        print(f"✅ Profit targets scale with account size")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
