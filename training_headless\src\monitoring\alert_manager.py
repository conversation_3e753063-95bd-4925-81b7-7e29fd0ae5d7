"""
Alert Management System for Grid Trading Bot
"""
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from typing import Dict, Any, List, Optional
from dataclasses import asdict
import json
from datetime import datetime, timedelta
import asyncio

# Configure logging
logger = logging.getLogger(__name__)

class AlertManager:
    """Manages alerting for the trading system"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config.get('alerting', {})
        self.email_config = self.config.get('email', {})
        self.alert_history = []
        self.max_history = 1000
        self.alert_cooldowns = {}  # Track when we last sent alerts for each type
        
    async def send_alert(self, 
                        alert_type: str, 
                        message: str, 
                        severity: str = 'warning',
                        data: Optional[Dict] = None) -> bool:
        """
        Send an alert through configured channels
        
        Args:
            alert_type: Type of alert (e.g., 'api_error', 'high_cpu')
            message: Human-readable message
            severity: 'info', 'warning', 'error', or 'critical'
            data: Additional data to include in the alert
            
        Returns:
            bool: True if alert was sent successfully
        """
        # Check if we're in cooldown for this alert type
        if self._is_in_cooldown(alert_type, severity):
            logger.debug(f"Alert {alert_type} is in cooldown, skipping")
            return False
            
        # Prepare alert data
        alert = {
            'timestamp': datetime.utcnow().isoformat(),
            'type': alert_type,
            'severity': severity,
            'message': message,
            'data': data or {}
        }
        
        # Log the alert
        logger.log(
            self._get_log_level(severity),
            f"[{severity.upper()}] {alert_type}: {message}"
        )
        
        # Send alerts through all configured channels
        success = True
        
        # Email alert
        if self.email_config.get('enabled', False):
            if not await self._send_email_alert(alert):
                success = False
                
        # TODO: Add other alert channels (Slack, Telegram, etc.)
        
        # Store in history
        self.alert_history.append(alert)
        self.alert_history = self.alert_history[-self.max_history:]
        
        # Update cooldown
        self._update_cooldown(alert_type, severity)
        
        return success
    
    async def _send_email_alert(self, alert: Dict[str, Any]) -> bool:
        """Send alert via email"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.email_config.get('from')
            msg['To'] = ', '.join(self.email_config.get('to', []))
            msg['Subject'] = f"[{alert['severity'].upper()}] {alert['type']}"
            
            # Format message
            text = f"""
            Alert Type: {alert['type']}
            Severity: {alert['severity']}
            Time: {alert['timestamp']}
            
            Message:
            {alert['message']}
            
            Additional Data:
            {json.dumps(alert.get('data', {}), indent=2)}
            """
            
            msg.attach(MIMEText(text, 'plain'))
            
            with smtplib.SMTP(
                self.email_config.get('smtp_server'),
                self.email_config.get('smtp_port', 587)
            ) as server:
                if self.email_config.get('use_tls', True):
                    server.starttls()
                
                if self.email_config.get('username') and self.email_config.get('password'):
                    server.login(
                        self.email_config['username'],
                        self.email_config['password']
                    )
                
                server.send_message(msg)
                
            return True
            
        except Exception as e:
            logger.error(f"Failed to send email alert: {e}")
            return False
    
    def _is_in_cooldown(self, alert_type: str, severity: str) -> bool:
        """Check if this alert type is in cooldown"""
        cooldown_key = f"{alert_type}:{severity}"
        last_alert = self.alert_cooldowns.get(cooldown_key)
        
        if not last_alert:
            return False
            
        # Get cooldown duration based on severity
        cooldown_seconds = {
            'info': 3600,      # 1 hour
            'warning': 1800,   # 30 minutes
            'error': 300,      # 5 minutes
            'critical': 60     # 1 minute
        }.get(severity, 300)
        
        return (datetime.utcnow() - last_alert).total_seconds() < cooldown_seconds
    
    def _update_cooldown(self, alert_type: str, severity: str):
        """Update the cooldown for this alert type"""
        cooldown_key = f"{alert_type}:{severity}"
        self.alert_cooldowns[cooldown_key] = datetime.utcnow()
    
    @staticmethod
    def _get_log_level(severity: str) -> int:
        """Convert severity to logging level"""
        return {
            'info': logging.INFO,
            'warning': logging.WARNING,
            'error': logging.ERROR,
            'critical': logging.CRITICAL
        }.get(severity.lower(), logging.INFO)
