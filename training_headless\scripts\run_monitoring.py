#!/usr/bin/env python3
"""
Standalone monitoring service for the Grid Trading System
"""
import asyncio
import os
import sys
import logging
from pathlib import Path
from dotenv import load_dotenv

# Add project root to path
project_root = str(Path(__file__).parent.parent.absolute())
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from src.monitoring import run_monitoring_service
from src.config.monitoring_config import load_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('monitoring_service.log')
    ]
)

logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the monitoring service"""
    # Load environment variables
    load_dotenv()
    
    # Load configuration
    env_vars = dict(os.environ)
    config = load_config(env_vars)
    
    logger.info("Starting monitoring service...")
    logger.info(f"Check interval: {config['monitoring']['check_interval']} seconds")
    
    try:
        await run_monitoring_service(config)
    except KeyboardInterrupt:
        logger.info("Monitoring service stopped by user")
    except Exception as e:
        logger.critical(f"Monitoring service failed: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
