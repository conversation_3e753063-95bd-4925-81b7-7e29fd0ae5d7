"""
Main entry point for the Grid Trading System
"""
import os
import sys
from datetime import datetime, timedelta
from dotenv import load_dotenv
import pandas as pd

# Add src directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.binance_data_handler import BinanceDataHandler

def setup_environment():
    """Load environment variables and setup paths"""
    # Load environment variables from .env file
    env_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), '.env')
    if not os.path.exists(env_path):
        raise FileNotFoundError("Could not find .env file. Please create one with your Binance API keys.")
    
    load_dotenv(env_path)
    
    # Verify required environment variables
    required_vars = ['BINANCE_API_KEY', 'BINANCE_API_SECRET']
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    if missing_vars:
        raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

def initialize_data_handler():
    """Initialize and return the Binance data handler"""
    api_key = os.getenv('BINANCE_API_KEY')
    api_secret = os.getenv('BINANCE_API_SECRET')
    db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'trading_data.db')
    
    return BinanceDataHandler(api_key, api_secret, db_path)

def main():
    """Main function to run the trading system"""
    try:
        print("=== Grid Trading System Initialization ===")
        
        # Setup environment
        print("\n[1/4] Setting up environment...")
        setup_environment()
        
        # Initialize data handler
        print("[2/4] Initializing data handler...")
        handler = initialize_data_handler()
        
        # Update market data
        print("[3/4] Updating market data...")
        handler.update_all_data()
        
        # Display sample data
        print("\n[4/4] Fetching latest market data...")
        btc_data = handler.get_market_data('BTCUSDT', lookback=5)
        eth_data = handler.get_market_data('ETHUSDT', lookback=5)
        
        print("\n=== BTC/USDT Latest Data ===")
        print(btc_data[['timestamp', 'close', 'rsi', 'vwap']].to_string(index=False))
        
        print("\n=== ETH/BTC Ratio ===")
        print(f"Latest: {eth_data['eth_btc_ratio'].iloc[-1]:.6f}")
        print(f"24h Change: {(eth_data['eth_btc_ratio'].iloc[-1] / eth_data['eth_btc_ratio'].iloc[0] - 1) * 100:.2f}%")
        
        print("\n=== System Ready ===")
        print("Data handler initialized successfully.")
        print(f"Database location: {os.path.abspath(handler.db_path)}")
        
    except Exception as e:
        print(f"\n!!! Error initializing system: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
