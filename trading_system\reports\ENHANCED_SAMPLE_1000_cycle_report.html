
        <!DOCTYPE html>
        <html>
        <head>
            <title>Enhanced 1000-Cycle Evaluation Report</title>
            <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
            <style>
                
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; padding: 20px; background: #f5f7fa; }
        .container { max-width: 1600px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px; border-radius: 15px; text-align: center; margin-bottom: 30px; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { margin: 10px 0 0 0; opacity: 0.9; font-size: 1.2em; }
        .header-stats { display: flex; justify-content: center; gap: 30px; margin-top: 20px; }
        .stat-item { text-align: center; }
        .stat-value { display: block; font-size: 2em; font-weight: bold; }
        .stat-label { display: block; opacity: 0.8; }
        .section { background: white; margin: 20px 0; padding: 25px; border-radius: 10px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); }
        .section h2 { color: #2c3e50; margin-top: 0; border-bottom: 3px solid #3498db; padding-bottom: 10px; }
        .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .summary-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #3498db; }
        .summary-card h3 { margin-top: 0; color: #2c3e50; }
        .summary-card ul { margin: 0; padding-left: 20px; }
        .summary-card li { margin: 8px 0; }
        .chart-container { margin: 25px 0; height: 400px; }
        .chart-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .trades-table { width: 100%; border-collapse: collapse; margin: 20px 0; font-size: 0.9em; }
        .trades-table th { background: #3498db; color: white; padding: 12px 8px; text-align: center; }
        .trades-table td { padding: 8px; border-bottom: 1px solid #eee; text-align: center; }
        .trades-table .profit { background: #d4edda; }
        .trades-table .loss { background: #f8d7da; }
        .table-container { overflow-x: auto; }
        .trade-summary { background: #e8f4fd; padding: 15px; border-radius: 8px; margin-bottom: 20px; }
        .trade-summary p { margin: 5px 0; }
        .equity-stats { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; }
        .stat-card h4 { margin-top: 0; color: #2c3e50; }
        .stat-card p { margin: 8px 0; }
        .commission-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .commission-card { background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; }
        .commission-card h3 { margin-top: 0; color: #856404; }
        .commission-card p { margin: 8px 0; }
        .note { font-style: italic; color: #666; margin-top: 10px; }
        
            </style>
        </head>
        <body>
            <div class="container">
                
        <div class="header">
            <h1>🎯 Enhanced 1000-Cycle Comprehensive Evaluation Report</h1>
            <p>60-Day Training + 30-Day Out-of-Sample Testing</p>
            <p>Optimized for Composite Metrics | Generated: 2025-05-26 14:52:18</p>
            <div class="header-stats">
                <div class="stat-item">
                    <span class="stat-value">1000</span>
                    <span class="stat-label">Valid Cycles</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">0.460</span>
                    <span class="stat-label">Avg Composite</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">+0.5%</span>
                    <span class="stat-label">Avg Return</span>
                </div>
                <div class="stat-item">
                    <span class="stat-value">520</span>
                    <span class="stat-label">Profitable</span>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>📊 Executive Summary</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>Performance Overview</h3>
                    <ul>
                        <li><strong>Success Rate:</strong> 100.0%</li>
                        <li><strong>Profitable Cycles:</strong> 520/1000 (52.0%)</li>
                        <li><strong>Best Composite Score:</strong> 0.969</li>
                        <li><strong>Worst Composite Score:</strong> 0.110</li>
                    </ul>
                </div>
                <div class="summary-card">
                    <h3>Return Statistics</h3>
                    <ul>
                        <li><strong>Mean Return:</strong> +0.54%</li>
                        <li><strong>Median Return:</strong> +0.63%</li>
                        <li><strong>Best Return:</strong> +41.57%</li>
                        <li><strong>Worst Return:</strong> -54.00%</li>
                    </ul>
                </div>
                <div class="summary-card">
                    <h3>Trading Activity</h3>
                    <ul>
                        <li><strong>Avg Trades/Cycle:</strong> 24.9</li>
                        <li><strong>Min Trades:</strong> 12</li>
                        <li><strong>Max Trades:</strong> 44</li>
                        <li><strong>Std Deviation:</strong> 5.1</li>
                    </ul>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>📈 Performance Distribution</h2>
            <div class="chart-grid">
                <div class="chart-container" id="composite-distribution"></div>
                <div class="chart-container" id="returns-distribution"></div>
            </div>
            <div class="chart-container" id="performance-categories"></div>
        </div>
        
                
        <div class="section">
            <h2>📋 Composite Metrics Breakdown</h2>
            <div class="composite-breakdown">
                
            <div class="metric-breakdown">
                <h4>Win Rate</h4>
                <p><strong>Mean:</strong> 0.5200</p>
                <p><strong>Std:</strong> 0.1500</p>
                <p><strong>Range:</strong> 0.2000 - 0.8500</p>
                <p><strong>Median:</strong> 0.5100</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Equity Growth</h4>
                <p><strong>Mean:</strong> 0.0800</p>
                <p><strong>Std:</strong> 0.1200</p>
                <p><strong>Range:</strong> -0.2500 - 0.3500</p>
                <p><strong>Median:</strong> 0.0700</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Sortino Ratio</h4>
                <p><strong>Mean:</strong> 1.2000</p>
                <p><strong>Std:</strong> 0.8000</p>
                <p><strong>Range:</strong> -0.5000 - 3.5000</p>
                <p><strong>Median:</strong> 1.1000</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Calmar Ratio</h4>
                <p><strong>Mean:</strong> 1.1000</p>
                <p><strong>Std:</strong> 0.9000</p>
                <p><strong>Range:</strong> -0.3000 - 4.2000</p>
                <p><strong>Median:</strong> 0.9000</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Profit Factor</h4>
                <p><strong>Mean:</strong> 1.3000</p>
                <p><strong>Std:</strong> 0.4000</p>
                <p><strong>Range:</strong> 0.6000 - 2.8000</p>
                <p><strong>Median:</strong> 1.2000</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Max Drawdown</h4>
                <p><strong>Mean:</strong> 0.1800</p>
                <p><strong>Std:</strong> 0.0800</p>
                <p><strong>Range:</strong> 0.0500 - 0.4500</p>
                <p><strong>Median:</strong> 0.1600</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Risk Of Ruin</h4>
                <p><strong>Mean:</strong> 0.1500</p>
                <p><strong>Std:</strong> 0.1200</p>
                <p><strong>Range:</strong> 0.0100 - 0.6500</p>
                <p><strong>Median:</strong> 0.1200</p>
            </div>
            
            <div class="metric-breakdown">
                <h4>Trade Frequency</h4>
                <p><strong>Mean:</strong> 3.2000</p>
                <p><strong>Std:</strong> 1.8000</p>
                <p><strong>Range:</strong> 0.5000 - 8.5000</p>
                <p><strong>Median:</strong> 2.9000</p>
            </div>
            
            </div>
        </div>
        
                
        <div class="section">
            <h2>🏆 Best Performing Cycles</h2>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Cycle</th>
                            <th>Composite Score</th>
                            <th>Return</th>
                            <th>Final Balance</th>
                            <th>Total Trades</th>
                            <th>Winning</th>
                            <th>Losing</th>
                            <th>Max Drawdown</th>
                            <th>Commission</th>
                        </tr>
                    </thead>
                    <tbody>
                        
            <tr>
                <td>1</td>
                <td>847</td>
                <td>0.9234</td>
                <td>+28.50%</td>
                <td>$385.50</td>
                <td>32</td>
                <td>22</td>
                <td>10</td>
                <td>8.20%</td>
                <td>$12.45</td>
            </tr>
            
                    </tbody>
                </table>
            </div>
        </div>
        
                
        <div class="section">
            <h2>📋 Trade-by-Trade Analysis (Best Cycle #847)</h2>
            <div class="trade-summary">
                <p><strong>Total Trades:</strong> 3</p>
                <p><strong>Total Commission Paid:</strong> $12.45</p>
                <p><strong>Final Balance:</strong> $385.50</p>
                <p><strong>Total Return:</strong> +28.50%</p>
            </div>
            <div class="table-container">
                <table class="trades-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Direction</th>
                            <th>Entry Time</th>
                            <th>Exit Time</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Size</th>
                            <th>Gross P&L</th>
                            <th>Commission</th>
                            <th>Net P&L</th>
                            <th>Return %</th>
                            <th>Duration</th>
                            <th>Exit Reason</th>
                        </tr>
                    </thead>
                    <tbody>
                        
            <tr class="profit">
                <td>1</td>
                <td>LONG</td>
                <td>2025-05-24 14:52</td>
                <td>2025-05-24 18:52</td>
                <td>$43250.00</td>
                <td>$43380.00</td>
                <td>0.006944</td>
                <td>$+9.03</td>
                <td>$6.00</td>
                <td>$+3.03</td>
                <td>+1.01%</td>
                <td>4.2h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="profit">
                <td>2</td>
                <td>SHORT</td>
                <td>2025-05-24 22:52</td>
                <td>2025-05-25 00:52</td>
                <td>$43420.00</td>
                <td>$43280.00</td>
                <td>0.006912</td>
                <td>$+9.67</td>
                <td>$6.00</td>
                <td>$+3.67</td>
                <td>+1.22%</td>
                <td>2.0h</td>
                <td>TAKE_PROFIT</td>
            </tr>
            
            <tr class="loss">
                <td>3</td>
                <td>LONG</td>
                <td>2025-05-25 02:52</td>
                <td>2025-05-25 03:52</td>
                <td>$43180.00</td>
                <td>$43120.00</td>
                <td>0.006956</td>
                <td>$-4.17</td>
                <td>$6.00</td>
                <td>$-10.17</td>
                <td>-3.39%</td>
                <td>1.0h</td>
                <td>STOP_LOSS</td>
            </tr>
            
                    </tbody>
                </table>
            </div>
            
        </div>
        
                
        <div class="section">
            <h2>📈 Equity Curve & Drawdown Analysis (Best Cycle)</h2>
            <div class="chart-grid">
                <div class="chart-container" id="equity-curve"></div>
                <div class="chart-container" id="drawdown-chart"></div>
            </div>
            <div class="equity-stats">
                <div class="stat-card">
                    <h4>Equity Statistics</h4>
                    <p><strong>Starting Balance:</strong> $300.00</p>
                    <p><strong>Final Balance:</strong> $385.50</p>
                    <p><strong>Peak Balance:</strong> $1198.99</p>
                    <p><strong>Total Return:</strong> +28.50%</p>
                </div>
                <div class="stat-card">
                    <h4>Drawdown Statistics</h4>
                    <p><strong>Max Drawdown:</strong> 11.61%</p>
                    <p><strong>Avg Drawdown:</strong> 1.83%</p>
                    <p><strong>Recovery Factor:</strong> 2.45</p>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>💰 Commission Analysis (0.1% of Trade Size)</h2>
            <div class="commission-grid">
                <div class="commission-card">
                    <h3>Commission Details</h3>
                    <p><strong>Total Commission Paid:</strong> $12.45</p>
                    <p><strong>Commission as % of Initial Capital:</strong> 4.15%</p>
                    <p><strong>Commission Rate:</strong> 0.1% per trade (entry + exit = 0.2% total)</p>
                    <p><strong>Number of Trades:</strong> 3</p>
                </div>
                <div class="commission-card">
                    <h3>Impact Analysis</h3>
                    <p><strong>Gross Return:</strong> +32.65%</p>
                    <p><strong>Net Return (after commission):</strong> +28.50%</p>
                    <p><strong>Commission Impact:</strong> -4.15%</p>
                    <p><strong>Avg Commission per Trade:</strong> $4.15</p>
                </div>
            </div>
        </div>
        
                
        <div class="section">
            <h2>🎯 Key Insights & Recommendations</h2>
            <div style="background: #e8f4fd; padding: 20px; border-radius: 10px; border-left: 5px solid #3498db;">
                <h3>Performance Analysis:</h3>
                <ul>
                    <li><strong>Success Rate:</strong> 100.0% of cycles completed successfully</li>
                    <li><strong>Profitability:</strong> 520/1000 cycles were profitable (52.0%)</li>
                    <li><strong>Consistency:</strong> Composite score std deviation of 0.176</li>
                    <li><strong>Best Performance:</strong> Top cycle achieved 0.969 composite score</li>
                    <li><strong>Commission Impact:</strong> Average commission reduces returns by approximately 0.1-0.3% per cycle</li>
                </ul>

                <h3>Trading Pattern Analysis:</h3>
                <ul>
                    <li><strong>Activity Level:</strong> Average 24.9 trades per 30-day test period</li>
                    <li><strong>Trade Frequency:</strong> Approximately 0.8 trades per day</li>
                    <li><strong>Consistency:</strong> Trade count std deviation of 5.1</li>
                </ul>

                <h3>Composite Metrics Performance:</h3>
                <ul>
                    <li><strong>Excellent Cycles (0.8+):</strong> 4.7% - These represent the model's peak potential</li>
                    <li><strong>Good Cycles (0.6-0.8):</strong> 19.8% - Solid performance with room for improvement</li>
                    <li><strong>Acceptable Cycles (0.4-0.6):</strong> 40.2% - Moderate performance, needs optimization</li>
                    <li><strong>Poor Cycles (<0.4):</strong> 35.3% - Requires significant improvement</li>
                </ul>

                <h3>Recommendations:</h3>
                <ul>
                    <li><strong>Model Optimization:</strong> Focus on improving cycles with composite scores below 0.4</li>
                    <li><strong>Pattern Analysis:</strong> Study top-performing cycles for common characteristics</li>
                    <li><strong>Risk Management:</strong> Implement dynamic position sizing based on market volatility</li>
                    <li><strong>Commission Optimization:</strong> Consider trade frequency vs commission cost trade-offs</li>
                    <li><strong>Ensemble Methods:</strong> Combine multiple best-performing models for improved consistency</li>
                    <li><strong>Market Conditions:</strong> Analyze performance correlation with different market regimes</li>
                </ul>
            </div>
        </div>
        
            </div>

            <script>
                
        // Composite Score Distribution
        var compositeData = [{
            x: [0.5450253291350622, 0.43763749724034917, 0.5768303895859577, 0.31272026286107873, 0.7912520150248289, 0.25551240551103194, 0.6881329332060586, 0.3435380843421775, 0.38068235880298595, 0.6057807293157244, 0.7521795663735749, 0.2224844909524551, 0.5388424144876214, 0.46754522718128977, 0.2729422872966437, 0.5537522756487642, 0.2781357198648346, 0.4865173123109916, 0.34079793792604973, 0.38867693549947224, 0.41238753793435434, 0.5996129351767863, 0.2883368148090091, 0.5019453210315666, 0.36830928100259885, 0.3698155098190714, 0.5436216539861509, 0.3533554216819219, 0.3671679737267448, 0.460159881870548, 0.255614091651126, 0.372260607122721, 0.4982725511049295, 0.16790498801832698, 0.24552027056681588, 0.37863184382181336, 0.5224345198532747, 0.8315536893657393, 0.3442407179865271, 0.20300471799809244, 0.19751555196846152, 0.3521156086606212, 0.7526122882068005, 0.19061308209304514, 0.3947801030814969, 0.49021450246026566, 0.31022414646596597, 0.5996697519762714, 0.6458611734779044, 0.22913241874236245, 0.5395885613380541, 0.5129203868815061, 0.3922235887880575, 0.30208322534046395, 0.6720254863948251, 0.2363626412028133, 0.33218855698816185, 0.5321835780374407, 0.5274339118822695, 0.6752290405447193, 0.286784784245258, 0.6326955310422865, 0.4797588203443619, 0.46233960161479704, 0.41292076507403375, 0.6626216706055849, 0.22547645150238171, 0.19472631325288334, 0.7106364337743845, 0.5471259268800456, 0.6764406897612321, 0.5949513006554537, 0.3686331844561347, 0.647681594089203, 0.5204422043170887, 0.3579956167207573, 0.45019868148244124, 0.2874273621082396, 0.9688317829424781, 0.185475131339589, 0.16072019582468905, 0.3101126637856264, 0.5108821047114184, 0.24796952982146753, 0.2600865103551761, 0.5022385152996864, 0.4165830003722776, 0.46534755469634814, 0.6203432220135721, 0.35538726610828886, 0.3990147684646057, 0.3251053713188631, 0.4175210542461535, 0.40950296360859906, 0.5445298977581084, 0.5924494263709625, 0.5714635316320186, 0.22709850098745119, 0.3734886216400122, 0.3267468893422929, 0.42146894366005316, 0.7123857827375581, 0.28541456306308266, 0.32073467671970024, 0.4219707467056607, 0.3613018242558189, 0.3208292328654126, 0.4812157443698106, 0.4450396454452227, 0.3083216067662694, 0.1865248596765004, 0.6671209642524233, 0.3230861816657297, 0.8106904397733559, 0.7268624779330479, 0.5600115666404271, 0.25251807484063604, 0.3026367131697658, 0.4409343075438352, 0.30838441757145674, 0.4030767756381065, 0.15783798103185684, 0.6539484675661077, 0.5255357222701188, 0.38937940355097767, 0.8474249965963827, 0.3253810652707835, 0.8457341168997291, 0.19187902324203987, 0.1564417113803241, 0.6475072421501915, 0.3327311613726648, 0.4650661804385601, 0.585498583383987, 0.7091819148658178, 0.30895965688574845, 0.45690020985448243, 0.43203534079104833, 0.15682673240373848, 0.4965976164489597, 0.5680262921338401, 0.5290874288317615, 0.489700128880725, 0.5765934824671523, 0.5880226515133372, 0.29483324781602327, 0.7887252699128081, 0.6311482079936762, 0.35962344957080306, 0.334256738344413, 0.3610263724932955, 0.5509716385079184, 0.6597717408029787, 0.7841668542378165, 0.3789124450397727, 0.23636269088253983, 0.3431162103982127, 0.29857743151271865, 0.5826115926732514, 0.36936944480207523, 0.3365680689151713, 0.3803709336086669, 0.33509864628472635, 0.29180982743312744, 0.7361237378569393, 0.28007991946500144, 0.5813997202135222, 0.21402831326563004, 0.5369137296399967, 0.7931999797963083, 0.2993882373937172, 0.27222809420389016, 0.3939371518209034, 0.2304310853530665, 0.2603405139874889, 0.8990445968957747, 0.6264569663428755, 0.25547463900451717, 0.6755464820727367, 0.34737662776390593, 0.5012034395199636, 0.4182945744190869, 0.6333628433797766, 0.5216687800053863, 0.3043019293649878, 0.2604822434078931, 0.21606489097670328, 0.56796832261486, 0.2118616604672453, 0.43065859851152566, 0.4263940132473436, 0.3861550379871873, 0.5128322015468316, 0.23287022721041906, 0.24295175080024528, 0.5431303850842921, 0.3141191023457689, 0.5585175297697925, 0.43016535346872287, 0.5438622664468661, 0.3577015996444367, 0.29245845300102213, 0.821410637221695, 0.8520744172329066, 0.7532454260277696, 0.49517224444131425, 0.43166460505565096, 0.18048095257384836, 0.49054215105244503, 0.29878018408172363, 0.21552019355257845, 0.4879763079775986, 0.8515710298862179, 0.20971497538099199, 0.4001283254367546, 0.31521535814809337, 0.5419120836704947, 0.56009027100071, 0.34042616244920254, 0.3333779447261762, 0.6070134947353537, 0.29286243482991015, 0.2088205404942919, 0.49441566517279, 0.4392780077691928, 0.4641992278928083, 0.6462886935507823, 0.6116881752768792, 0.4377907168467894, 0.4147275105484006, 0.277439424903824, 0.20408196359045078, 0.4438732687152759, 0.5725578530906039, 0.679825390817757, 0.7106817635869059, 0.7122306708239645, 0.5480882251125689, 0.3228250582165916, 0.3707884871061313, 0.581530748316573, 0.48569824378567883, 0.6186418980452693, 0.44100767445326905, 0.4545792106485851, 0.4210771987450955, 0.37148935030798214, 0.7707820949884703, 0.34980868919281743, 0.5250163511879927, 0.31773894104804323, 0.7592219634689982, 0.2576651241114526, 0.2922609925625457, 0.6297356849022776, 0.5097615447714381, 0.3701830589627152, 0.5721907110011836, 0.42384054572176133, 0.3590210017517853, 0.377952370961674, 0.7038199003076319, 0.5957629661856713, 0.5405112383472065, 0.4603922522349906, 0.3976822454832233, 0.3379847358544687, 0.666481839096547, 0.22838784728980036, 0.19258701995730293, 0.5628048156683153, 0.3584962913055536, 0.6965629114497583, 0.612072315955965, 0.4386487820620075, 0.6163679927934383, 0.6559850038551793, 0.68070435259144, 0.386056019045698, 0.19083446430353873, 0.11846949891764572, 0.6607022107543722, 0.4387332614714753, 0.27836813295551477, 0.48433709784813894, 0.46879789210219214, 0.47769894010249636, 0.37707605150013823, 0.5805492382168679, 0.2612790619285985, 0.3928936773530477, 0.3034546321796163, 0.5741764960082593, 0.3124222190896465, 0.22153487871761468, 0.5111874045399186, 0.5014684369908526, 0.22237065444114823, 0.642856461886343, 0.47223831874559685, 0.36479377813059544, 0.15024119317392223, 0.5850584366462565, 0.49207031514846344, 0.5240844499584835, 0.5530800781358416, 0.17990085396488747, 0.3818541873748931, 0.6601934885411236, 0.23464491964972403, 0.4332292174835948, 0.22872947879230657, 0.5713253178796496, 0.43913036629670077, 0.5723904881129861, 0.541466352849569, 0.74179927749189, 0.7580787741532328, 0.20858770329645154, 0.5160478348516511, 0.7603874670455271, 0.4682123050997219, 0.8198667142918042, 0.39565371214003353, 0.6190387466807145, 0.7914896849889489, 0.270680179560124, 0.4154837813375232, 0.7808879791350186, 0.5350518001020504, 0.5563197003467595, 0.6417583926428952, 0.42399925400022587, 0.5769444350221404, 0.5273833630636562, 0.3523513989014271, 0.4212982962262928, 0.8114379215767826, 0.30721195594706074, 0.6736723606256231, 0.48720172362957426, 0.6304122458036105, 0.6361765256219909, 0.5153082661016225, 0.36477912855828953, 0.7451227163110977, 0.6575177260505297, 0.5068922506651314, 0.49988554314589495, 0.3017049865258713, 0.6079781167894847, 0.7511894912697562, 0.2777099114207664, 0.2965908622780519, 0.4517915303786987, 0.3290795133660148, 0.442836714130007, 0.4369023736329709, 0.28610309623453556, 0.7782217063212366, 0.2795525859801269, 0.4445477847210515, 0.32065073121550136, 0.7047306534022354, 0.4776428820618396, 0.4765799121731408, 0.7528103561065757, 0.3972300183955433, 0.5226901585349537, 0.31834544245580365, 0.44792213272212156, 0.5721143700584783, 0.2909190643119689, 0.4080955290891216, 0.5219864105059562, 0.23214988584373328, 0.571511473407683, 0.5178496910381827, 0.3797754394566234, 0.47204471241512336, 0.8028497516619523, 0.2468328328649, 0.43775141448434896, 0.8079320176843767, 0.31530470269506766, 0.37572749178489007, 0.6466532251280154, 0.4969064091038079, 0.42235431658562683, 0.24912608426841498, 0.6232839176951934, 0.800230545326061, 0.4545513199531538, 0.6199319265207559, 0.36418770990680993, 0.32659826709516815, 0.23584431728123875, 0.532106572595718, 0.7053496496110863, 0.42442721208098866, 0.6139838696312119, 0.4106469253015971, 0.5796295311557543, 0.443380313320735, 0.17100736768674513, 0.3118841613644431, 0.368475898951633, 0.4302672929432949, 0.5370343136774638, 0.21422757005303034, 0.23919802162145262, 0.41942545554251065, 0.6897853681617017, 0.5269819046471811, 0.2670100585526072, 0.19872957367971658, 0.3724227311202759, 0.4482617455715817, 0.6798315267884144, 0.2349006463832183, 0.6012415040665787, 0.31324444644998883, 0.708317535960899, 0.6016083504387559, 0.597666815127813, 0.6613309786691975, 0.3654752584535149, 0.7557137893429601, 0.424525997216027, 0.5809518604818075, 0.7296671296177718, 0.18198114394612502, 0.6498024833184413, 0.6228822632718518, 0.6057197215790838, 0.2755166112973846, 0.300251130337498, 0.4191062306600699, 0.4566040229695696, 0.36615964350660135, 0.6971208486537854, 0.11754928381070882, 0.39486194358874704, 0.24175261845996568, 0.7538428094530665, 0.5259578590038158, 0.42749102440603926, 0.2751773386112174, 0.5269056991453829, 0.2392062886928486, 0.5056550237092575, 0.7439742843662933, 0.7334422951795683, 0.3023782670393572, 0.714219875000799, 0.16715648338612407, 0.3969379738585114, 0.31057635275857365, 0.14996130096811322, 0.6007796701863575, 0.6706171846666764, 0.6495275806994341, 0.516405555642964, 0.5110793463022286, 0.7910290668436907, 0.3777911419001164, 0.5278262180739807, 0.3321206789432673, 0.6562634465324702, 0.6411005518115964, 0.505566834234326, 0.5131593129105564, 0.44797268759719844, 0.5145973329926343, 0.18894820152306235, 0.528565976081947, 0.7756460626269625, 0.3565218727737497, 0.5042095906450008, 0.4133211670424429, 0.34688818296106694, 0.6298941567964502, 0.3675462514033717, 0.6200528908283793, 0.4827361598401384, 0.21709500674993187, 0.3157777742875323, 0.48444760643566265, 0.4016221819934943, 0.3966001843906617, 0.45110634718092135, 0.33249129953298406, 0.2732787970037534, 0.5301172765719286, 0.11038424151480283, 0.3714614334936045, 0.5187378526263919, 0.7163151860336701, 0.41153259646667073, 0.777268630503453, 0.2716692782697704, 0.5396930420704976, 0.4219753772845519, 0.2400972163041833, 0.6235278015510674, 0.43767479718356406, 0.237466067773078, 0.5533811375456095, 0.19677030939547852, 0.5974488040193545, 0.30643000883231053, 0.8819027342902942, 0.3412074051495766, 0.43712605088917134, 0.5309198827360427, 0.34404004334117533, 0.6690827200947638, 0.4813950460540103, 0.16444440727906195, 0.21340491979856197, 0.6976948991696519, 0.4226996340873719, 0.42162657346176224, 0.4172787778866046, 0.2785096492805487, 0.416531006400784, 0.4125901046860563, 0.8628919312468458, 0.48322828693499487, 0.45553707224401485, 0.4449934566901538, 0.45892852976979104, 0.41717683781629855, 0.587624444564565, 0.4038479658703624, 0.7006023396134163, 0.39295793151005465, 0.6433019742329079, 0.672498167871046, 0.3421539510222518, 0.21799969179089126, 0.767914640207109, 0.3976924622110186, 0.4805975585372475, 0.31673883213511966, 0.3432223212726473, 0.7314746215835697, 0.7141996524658407, 0.5175440276182065, 0.4178504068938108, 0.5759827166819041, 0.3541543530195913, 0.4970013383774309, 0.28130993084926725, 0.2660693247754147, 0.5255510710415725, 0.49278151660232794, 0.47509345345831044, 0.3786097044263246, 0.5265670256556128, 0.34883627054627675, 0.16155620883638985, 0.43477273844699416, 0.2548555670089504, 0.6022518991319787, 0.7325203454886186, 0.8170211031124107, 0.44901569479213865, 0.31551445518654525, 0.4757253098172921, 0.45387851007891344, 0.49321165737271566, 0.6603128361418434, 0.37535869442951153, 0.4903274394507353, 0.28681579459391643, 0.42849831883045886, 0.8378681286003541, 0.32216731687205524, 0.6185799760827716, 0.4034134218785661, 0.626138976684859, 0.6215068794311348, 0.2905489335640622, 0.5784729073001241, 0.21715565074565352, 0.24329224627687232, 0.45447362219893606, 0.7207839321598967, 0.5363402166505287, 0.30435965724948566, 0.16449582324517764, 0.5656734442511221, 0.44349729686281936, 0.47296758972896746, 0.5263648687071867, 0.6474506669838417, 0.43238325743143236, 0.7399123357417321, 0.32004806230518673, 0.1948176059789281, 0.6802012866707711, 0.2405318515222158, 0.4705207894855634, 0.729930012654575, 0.1898861657013382, 0.5570371337224348, 0.19058876652984824, 0.46255392714760313, 0.119187820576901, 0.24173129165427787, 0.31434934798854575, 0.17931003856996947, 0.5744946663101617, 0.5121267549254682, 0.7203235771800406, 0.25155427660197727, 0.39417303150399086, 0.20835716798597193, 0.33910178525973395, 0.7552429065330436, 0.5882367332692643, 0.2347319618444149, 0.1425183537661549, 0.138644342205183, 0.5927858164321117, 0.3088960011556713, 0.885009783553306, 0.4804410459628585, 0.31852854225735483, 0.22007053093124423, 0.6427015171212609, 0.34301118678025755, 0.5302242303586853, 0.4530425197088278, 0.455122600055609, 0.6190129068313085, 0.22264414221612952, 0.5001027015024291, 0.389104184550743, 0.60780255046067, 0.6424053189289306, 0.2754675131620823, 0.5685777316037103, 0.239174902068198, 0.412254674691627, 0.4585282946900613, 0.6148350273962545, 0.5955109085575945, 0.23550383412488907, 0.5687069382679787, 0.4632505360434116, 0.7975600070270498, 0.332945864791826, 0.3729497025412406, 0.8458487452063971, 0.6783733047098583, 0.22617924875253043, 0.4006936481075637, 0.541212443032099, 0.6272308314255807, 0.28580020460807587, 0.7358270587000856, 0.19671581622903775, 0.25831735981480064, 0.7108092379324604, 0.4443788089529984, 0.46912056431900495, 0.57942770049088, 0.301714660363391, 0.2071924951859211, 0.5361000754546921, 0.6029902563157405, 0.47260469191356336, 0.3898694729820905, 0.2510436139953372, 0.3793365482565151, 0.8284311074287664, 0.3954825597784607, 0.3934324884187429, 0.4341829923959609, 0.37784383084712714, 0.7151945643707606, 0.6060603357290005, 0.3389264434318834, 0.3976461367236679, 0.4227408686295735, 0.4597374237265879, 0.32192000568593604, 0.3359210785744904, 0.7168871734780717, 0.757955381977268, 0.3420448004126916, 0.38389663286520714, 0.5760304116141731, 0.18935396137976004, 0.692589436439187, 0.3174877299838404, 0.5483217378099025, 0.17485995432165016, 0.5690144151460454, 0.5509751715550009, 0.5315426656257098, 0.2735452929589746, 0.3473790598146621, 0.48076315338928477, 0.3380010475620079, 0.30001139803676935, 0.6888346754426207, 0.1489362412313645, 0.7283242114182612, 0.26271554973276356, 0.5170856480549557, 0.5086028236664456, 0.27342586110999356, 0.3424450621731624, 0.6121140254666877, 0.40249414681790674, 0.8780102040705695, 0.31149280327675005, 0.3115000547029615, 0.25213447306738856, 0.32057403447733, 0.43164481639367813, 0.8893931783728422, 0.3234543250442752, 0.1508508738429996, 0.36345172067376263, 0.43159953310221666, 0.718900840936824, 0.4563523300336706, 0.35724310907954815, 0.31401267763182883, 0.5630638860513466, 0.43563636262416383, 0.5563596575884799, 0.246897082301749, 0.3824071352123012, 0.6043575370449111, 0.19981007270075146, 0.26273759366536664, 0.40651211310561175, 0.6822598102941568, 0.7567985318991035, 0.27777803179110494, 0.5860581427318907, 0.7478620329958902, 0.596101371073025, 0.45930117881085897, 0.48565355777505825, 0.30353534631161844, 0.3436564137831777, 0.3900764043340975, 0.3814174612021246, 0.3670507571888998, 0.2529174848186257, 0.7404526642965331, 0.42751925821043113, 0.36769944322288783, 0.21704382297487534, 0.5558320583393394, 0.3086588493871484, 0.33668944619052055, 0.33459742386280455, 0.5727591090852465, 0.5028395901556796, 0.17170617420470402, 0.680719049485515, 0.5376644394211505, 0.4643443290514543, 0.2898102867724085, 0.647547863549045, 0.440338013776418, 0.3416626757853271, 0.2232812272622439, 0.4137622117069025, 0.7064605090764688, 0.3606512871025187, 0.20390419658386927, 0.35703380661165796, 0.3450569027324242, 0.44993365747097547, 0.2896623205660416, 0.3207084225689185, 0.2110158527837025, 0.520889123471941, 0.3129000967522799, 0.277121785319774, 0.6436255027391071, 0.7052752477785031, 0.5207514916941651, 0.7327794477418593, 0.6504593096060484, 0.4122623016301489, 0.24997036559886288, 0.35272744227399955, 0.3091044835349256, 0.5557529898891826, 0.38806178489935916, 0.17410502326733324, 0.36745060898644155, 0.28439912347029184, 0.43063031526980766, 0.6309616442296473, 0.22062202361602437, 0.6939362259517755, 0.45105046828247897, 0.5592914834108125, 0.3151509884968419, 0.41945495821478507, 0.6895225595093257, 0.6639142524271002, 0.4508298835285768, 0.3793890245896836, 0.4952604070067489, 0.4825280603513703, 0.7382795938177962, 0.3069203212982735, 0.3045663320638041, 0.3764353450987217, 0.25421577396846184, 0.5278300792734736, 0.18751618936179232, 0.7700088013968805, 0.5092076025445431, 0.5865167508440092, 0.1702020921386322, 0.5724582666112659, 0.5060427230412673, 0.5225540502063024, 0.4141419679062165, 0.5889283442695448, 0.28333260061015453, 0.5218891433726209, 0.21448025117883537, 0.22913976026372268, 0.7620643050810945, 0.29857350653468295, 0.2593152493367914, 0.6861883743999799, 0.6888319324500153, 0.35986098467134875, 0.8914451430245204, 0.3307626987623241, 0.6631661209959498, 0.49361862608275264, 0.3531281028271407, 0.6044087139447484, 0.20856421877586911, 0.4665086433720884, 0.3915765289789719, 0.5945309754788614, 0.4686507573619235, 0.5280193137313302, 0.5823733814853524, 0.3932444439143503, 0.4946651902787337, 0.8049420390871209, 0.48247727015396247, 0.3907415103062415, 0.6428151703956205, 0.3903418727533887, 0.7887145782969917, 0.7123553902822499, 0.355186190678054, 0.36700369051705506, 0.6194057773121295, 0.7054729075144796, 0.6525692852181665, 0.43463138803307577, 0.45646155633787255, 0.4140823448476626, 0.4377690816908797, 0.34009717058291383, 0.8275648192293804, 0.5720441022345882, 0.552263114845595, 0.6953479472709426, 0.18845576021728117, 0.5788914475767164, 0.714102265249281, 0.24345642566854384, 0.6955024434547549, 0.5730348243304572, 0.47216512836248137, 0.44905796504789286, 0.27822551615123486, 0.1634383077815013, 0.6583371946574417, 0.25724499055306904, 0.3388547036432329, 0.868248772481615, 0.5089967310259232, 0.5268423959549345, 0.38210561791243725, 0.6885461092060183, 0.21198501059208613, 0.5886887488393902, 0.232584436884482, 0.8261907245373407, 0.5491500231889066, 0.27128502612017436, 0.44730918417447496, 0.6031570036715969, 0.35460230199202825, 0.37582488544028503, 0.6832177688753365, 0.43910498612583726, 0.694031187675213, 0.5024028985889115, 0.5976620640361864, 0.7022853683814079, 0.7445167814289203, 0.26896838871245765, 0.5914428720756383, 0.7999241587212081, 0.6429096033021606, 0.48127988326181415, 0.6922545351127494, 0.8383465693410285, 0.5857667545504879, 0.6148437177036665, 0.551035002615848, 0.21349302486188448, 0.601387609229981, 0.24114922111176937, 0.5914670299971005, 0.20122869281425632, 0.25858135088900214, 0.40151021240395257, 0.3664553561228, 0.6675406675363454, 0.4909806087941071, 0.2831475437681451, 0.339452526716284, 0.4931530633501675, 0.4488296077217594, 0.5416313743781721, 0.5969721068560608, 0.6913970278379946, 0.560945127143601, 0.4437804333279408, 0.36049014043725436, 0.7873219362172192, 0.49157680749104304, 0.5476235309332366, 0.38155605237750745, 0.3717262076354396, 0.2862692371887313, 0.4605749913912728, 0.33726710302415297, 0.36814294107208334, 0.6420043309207412, 0.2215079909328715, 0.3074739979263944, 0.5456931686579495, 0.555195907771924, 0.7199063938717952, 0.17198546216652072, 0.6853222983372318, 0.2712945424769309, 0.40340443459859787, 0.2095769396048657, 0.12747735610075184, 0.6969175316079732, 0.28741188015796604, 0.5976499412847854, 0.35317228551474733, 0.7323959867046526, 0.38145286225765584, 0.41436541422823325, 0.31071616605009905, 0.26483936602667923, 0.640757069641774, 0.45018118050278577, 0.1465915004537407, 0.7512776924057062, 0.5166197533798299, 0.2843915117426501, 0.205778258395848, 0.2820206511681964, 0.4922079027760554, 0.6676429466692598, 0.39802820626102675, 0.9280082948097235, 0.36689949927761856, 0.43653141523266514, 0.24399900605092603, 0.4256637018392131, 0.6500851652820393, 0.8080814959335471, 0.6149206734659927, 0.17294205889223685, 0.24480116124009788, 0.1772212999831976, 0.2800979294525747, 0.3097073560697444, 0.30054900995578016, 0.5554756346404779, 0.23899345920284362],
            type: 'histogram',
            nbinsx: 30,
            name: 'Composite Scores',
            marker: {color: '#3498db'}
        }];

        Plotly.newPlot('composite-distribution', compositeData, {
            title: 'Distribution of Composite Scores (1000 Cycles)',
            xaxis: {title: 'Composite Score'},
            yaxis: {title: 'Frequency'}
        });

        // Returns Distribution
        var returnsData = [{
            x: [2.0102422843622394, 13.876946192052054, 13.787180139452136, -19.5214348437708, -12.312179386131245, 5.074608503405599, 10.250465412967543, 13.078779425736379, -17.609662904171692, 17.121420515076334, 20.652945346632936, -6.715574978888311, 8.253942548972248, 7.729496862861424, -19.786451522533703, 9.966449034269113, 24.204970136218247, 2.333059018139549, -31.296648895729835, -17.370077556452177, -24.691441732774415, 21.353916935690968, 8.753895333905707, -5.520539075017276, -30.525078595440856, -19.999309248364824, -7.5741544854572105, -3.0306632176389483, -3.6363318224291636, 22.12147963060554, -17.469587438564766, -32.14738137837072, -15.35467712713438, -5.414447380037017, -18.116205205024233, -10.256153819234465, -4.810503704791657, 22.92190762964146, -15.565382367661798, -11.79215738506058, 7.9111044608579615, -15.169674398577367, 13.078548493484213, 0.9667347163916586, -2.448214538700569, -15.015945740597346, 17.135936041394864, 35.310256948746705, 8.105937659208736, -23.834387720666754, 2.436540624875339, 16.847902108789988, -16.07337816288093, -7.320185314127414, 12.087849636945695, 16.654861339706645, 8.957627195608739, 14.253019542983932, -4.394028350146378, 12.823027462087076, -17.995339888252325, 18.146313198622536, 19.268294611784206, 10.30045353158934, -3.5111588019222317, 5.555274463500821, 27.517291791916172, -11.31278800322293, 20.59993000940682, 7.083373973277825, 3.9903626184818872, -18.169735412041376, -1.1911931494068773, 9.093210399273469, 29.098020071890005, -20.48346851760497, 36.421528873216765, 1.818589530938679, 18.02263376977726, -15.675298419780141, 0.6647135359804359, 3.5149167081978305, 16.059909377313154, -22.609799736654974, 20.322055404184695, 7.421232856579693, 11.123433429609682, -1.3141339225714082, -3.137711659348607, -3.690956706740827, -1.6286801031820701, -29.937708826060835, 13.899281077476877, 16.94786950844871, -8.944969357316902, -13.338780254081371, -2.544420653114515, -4.5698882542466075, -9.755078178476015, -6.096520989874098, 5.322165465138744, -8.419227264707548, -27.55932129145877, -54.0009993366166, 16.138178426802078, -4.725913628885914, 13.86132036637282, -15.066069937170884, 2.366110092997432, -1.133122889901177, -15.11208490694027, -10.641133581601515, 26.425684050432825, 26.194266730160408, 3.416812739295623, 9.653414677544113, -13.726232174424077, -22.917264854445456, 29.408922574355206, -34.79663986530443, -2.2825554545667233, -3.48217110963979, 27.814838944247434, 15.448829813540277, -35.10049594221174, 14.977998071726542, -4.620916429441529, 13.173293317604463, 6.156563935545417, -30.363924699607587, 3.096369049559958, 3.6187374678353557, 12.193635277409618, 4.241292102637954, 4.539293596396411, -7.158942925276506, 13.106106835658558, 2.5085117618818997, -23.753255817929517, -16.50246375917355, -20.34456132154164, 23.833712277340307, 14.111927534186673, 0.4026817522917925, -4.806887277461256, -15.330740143412438, 21.118845290986517, 30.85320209809689, -15.897072630695703, 15.491479371802768, -4.495767523539993, -1.186433948656998, 26.05057139395908, 7.878011351086677, 0.33455990658344437, -0.5310692075939754, -34.26078859987013, 0.191400676889109, -6.440380366603492, -30.580756522621183, -14.308467448077723, -14.456999653465026, 13.537885704345957, -24.14821308983657, -1.4781692685910546, -0.7630113101357754, -17.886906570313712, -16.862121648182004, -22.967151029998334, 19.040137520064754, -10.424536167923343, -5.286521210040362, 11.488185756239623, -18.000839905769965, -10.615766155664074, 6.549376005554068, -4.702044651949748, -21.57049209210582, 2.0901153099960155, -11.926319615115176, 6.329482263475697, -11.368254220628494, 11.835592111009717, 9.27501937284842, -13.42079929098079, -19.48504230353086, -3.468577424360181, -1.0437619451566733, -1.0336670730761823, 25.547996543899504, 3.0295557264157313, -16.506150103042376, -11.363344179130879, -20.339629363788504, -31.329793528000174, 15.991154125183682, -45.19901874840376, 9.321572265789586, -11.020958600128461, -5.686078219361767, -21.163777547798997, 21.025563873556553, 15.130524813486215, 21.32302820034282, 23.606322303152073, 4.451082110336057, -15.934356562828185, -8.971569502083899, -9.133720203493036, -14.06307491397138, -6.7032282676872, -0.012334631383963846, 18.296873288162292, 5.031477288048761, -0.012177643996863718, 2.2624405259425124, -6.240397824031623, 21.721697383947554, -15.179203187917633, 16.549356723203704, -9.712805101260663, -11.84733063339523, -10.957607125044902, 6.18487692122711, 14.927252048282353, -16.740928045892918, -5.29579650451489, -8.822896812594855, 21.80276295797626, 6.464074141956836, -24.51868175222821, -12.975707731209273, 19.726420934922416, -2.1717884546685715, 3.9537423465059254, -1.3755604818767022, -4.299907765825154, 19.74985715664735, 25.130228597357085, -22.733499250172258, 23.074134938271797, -5.31084124435158, 14.403775944426645, 23.15320707221612, -0.1761489355544823, -2.919128804010386, 9.419457348187343, -5.319434044434988, 15.647104801127746, 4.142140886638749, -15.399509146775182, 0.7499887602097459, -29.902325212910586, -14.651098999231815, 4.831546856067186, -13.919402975672227, -5.81836957032208, 9.093756267882434, -1.2020820399912826, 6.9290648773468, -19.528519693628052, 20.040593170639283, 3.827220586622871, 25.178857546321147, 38.21325968358099, -13.464384661459063, -12.723149784226244, 5.045094536615391, 5.722640503159267, 18.86745946855162, -7.79212726254705, 1.8623967966053092, 6.368872521730793, 15.417309230487062, 24.641865401115354, 5.116061150803821, -12.038412000084769, 16.926820122563516, -19.988693934898144, -7.576083265347999, 9.166555063689046, 11.568738242373296, 0.4741996167043252, -15.365870824249777, -41.49840670412498, 12.47481223634303, -1.794275488386461, 22.24005301040306, 7.970420449428046, -14.740256705627171, 14.203661590768725, 15.720329307247425, -1.2854772152871679, 16.4856075803181, -14.995544996948848, 4.3947165411185445, 10.571125815781707, 9.126068512255404, 15.45826218339068, -2.9898577084475875, 14.686557532445946, -7.239691815915533, -6.535410434127902, -6.315069509699965, 31.099825872145026, -9.12975142137917, 18.24943408168804, -25.438400524583056, 13.854865340890235, 0.34750212683503, 26.318403195893417, -16.77923411303327, -11.959963269405804, 0.2523443247952686, 26.972027438606784, -11.262377199156521, -23.060999586450396, 8.717214048479391, 12.09141252329816, -13.207088989597944, 18.17518608023917, -13.538664775186845, 13.827531714466568, 8.54828167514903, -6.249584820861262, 10.178159978090653, 16.869523203260268, 9.109011488170264, -0.6475126910892133, 24.780775290751908, 11.875887678837353, -14.325375999091683, 14.963654221013396, -6.570613786259154, 4.948453214636967, 13.144072077278672, -6.2440687699725075, 1.835259087665488, 22.099813454304908, 21.975001654423373, 0.6911598022558141, 18.24105741790538, 3.5673592152357863, 3.4545526514127456, -7.597182967461319, 4.052670975712373, 12.311630961029058, -16.12268235468382, -1.773712461483686, 0.6606305047012881, 30.05720849553963, -6.010410583752352, 4.36995503918369, 6.3426779757015055, -26.15078096024105, 2.3895762593665326, 15.204276184756713, 10.829790190736523, -6.510690587708157, 11.998880438269145, -1.9427954495174578, -23.341860074727922, -1.5202341544196125, 3.5951551282199308, -8.572332564969745, 39.27454930379981, 23.031388635434688, -25.703097085658978, 7.199542948487585, 21.244454960157846, -20.442987311140026, 3.1547471211073104, -2.7666909370491166, -17.11739323748534, 7.693254569183445, 15.092435029326253, 24.6207138712873, -1.7404615243891857, 40.02961063243203, -28.869501300951327, 17.849733678515697, -30.4212977587374, 3.3527395624534932, 10.824745999159765, 4.123794215030102, -7.2838528538394955, -1.9118101363181967, -9.404364213459697, 30.98689246174329, 2.98342729232347, -12.425544478460658, 19.40630188093411, -0.9201499353699578, 8.055213045165669, 9.757986771672346, 16.298089126831226, -20.458629102572402, 1.553875187669242, 1.4466679671371647, 10.95293271611394, 5.212346075165966, 2.4387721955281316, -2.05415141515247, 0.40820726646002226, 25.683718106706433, 38.03379096581904, 1.78765768736457, 0.5004533027464517, -21.934551413345247, -18.687527596604724, -7.281248644146313, -8.725948309561396, 0.6811908455369311, 25.63084211084248, -4.48176535270733, -2.872184154765136, -17.135430086088082, 19.66317087154379, 5.4490564641371835, -14.375545972715413, -10.969009807713299, -6.09983444752459, 1.3753039786711803, 28.21495695220279, 29.658480380029257, -6.823365937195295, -25.198758281857952, 4.684164378482627, -10.240568430914076, 5.261248635300702, 4.080518130973191, 0.04958212999465239, 20.1931222597636, -4.77065812402609, 19.942534669728783, -18.115743457763184, 33.67269128467797, 26.314639493740824, 0.3037949617064708, -22.344502001540747, -5.858391756094999, 10.72531303734154, -5.547249797548973, 11.683603943535246, 34.450747631547685, -9.555744080741855, -16.45207365637132, -1.5339157784127329, 19.17139417178446, -21.92828521614363, 2.5193710813886914, 24.440523934051882, -11.594534061884467, -0.7642766940962087, 1.5187628768145833, -2.765181362516545, 7.776117230506383, -1.0254468580143574, -23.184003238013393, 1.985046341198255, 25.119115763893593, 11.94719472708918, 14.604786913772726, 5.754429652745636, 17.709392818903044, -12.13062961887179, -9.512674701511534, 8.746558885264845, 21.528488125346673, -2.9713757295597034, -6.63917217135592, -0.44839642346359776, 26.297369074739727, 10.447031827523892, -28.833562937585963, 0.6052581261574808, 5.04945493099479, -26.118852008070743, 0.4281212465790851, -2.350561594095585, -11.50938542510886, 3.1114235914182675, 21.485083870278462, 3.6355626067938154, 2.745065844503968, -6.0577648209811255, -14.090303306612048, 3.1925709855154665, 2.9802589414664498, -3.5643825158141125, -10.888497131446318, -8.71024113054754, 8.685793756297437, -0.6972494707006045, -2.087925573177354, -1.9933339869256077, 8.260926961008757, 14.703810980165727, -0.6122591462383076, -2.9360236371051567, -26.936875916147763, 17.265296191045387, 13.553756719565996, 6.688564318075477, 17.73444233435208, -0.42333975438832727, -16.247775519779903, -12.706111146970569, -24.608299938538988, 20.182380328254965, -13.876607403574678, 23.438872553687094, -8.363944946851715, -8.14361812363636, -14.415471963252152, -15.586778425779508, 25.018865394875483, -14.263151196697187, 33.10144278663371, -20.13093369445359, 16.999399647814947, -0.17876941382482858, -2.808393737892783, -17.07131165687704, -6.771073689522286, -15.374289945490066, -23.223656496511605, 15.956789425739593, -7.531346602536329, 27.265227741874714, 17.000325726392333, 27.46148272005155, -8.21324356761848, 4.04206610196931, -37.3100643952158, 4.6183714317593125, -3.2270297355557425, 9.848576932421265, 7.491579073499925, -3.8013337084699756, -53.83836741494141, 11.074662032983738, 3.7019180547406982, 22.070381039432764, 1.6372850669847514, -7.353267200838288, 21.25930306677166, -20.288010695193748, -5.724227959407156, -9.487149700880671, 1.4821730025303343, -44.576470620124965, 21.79453493365578, -9.496578194316427, 0.7383404408608101, 5.346368375898413, 20.937348423154255, -25.92925202311071, -2.978219021543377, 19.192673910572903, -6.763756040972444, -22.122849076489434, -7.058351734836787, -12.072956032515282, -0.6526003542758687, 6.166266595191686, 27.065979225480014, -6.960471483062239, -13.778131537021984, 8.896708760754697, -0.6223054594581399, -17.937777000173238, 7.26573874665174, -6.780045783144659, 16.6351751815723, -22.498743167647902, 16.73479224133165, 14.217353503200451, -24.942950076469927, -7.705339744923144, 31.015827918627945, 6.081007725974716, 19.238049400765856, -35.74614910294633, 41.56542331945852, 11.078403310608312, -9.597923854793763, -2.5924878678326477, 16.815177539635947, 12.157725300875704, -1.7144729528461027, -4.8041823523598515, 10.416006260388748, -24.125553296202657, -3.569120689213795, -22.70295787439269, 15.255108136430058, -8.13720956549979, -10.858936944150134, -19.288157432341453, -2.311588373615867, 5.537259409330278, -26.116818062589918, -23.57302902595924, 8.887193444366025, -15.453825921508296, -1.8712106479607629, -7.874333374789394, 8.450985307629775, 10.245037031858432, -2.3628343827480247, -10.76832589567359, -4.554256314275551, -3.0885545088943207, -22.638300304496816, 21.103354055474902, 11.261899524780468, 30.790011884450735, 36.048987516596945, -26.168362380549688, 3.97520095545406, 39.67309395305214, -5.729911080480226, -0.6042037541584655, -32.38033913050243, 4.2543294736516115, 29.774926745663606, 25.293006480828225, -7.14976932277815, -12.133694598231493, -25.35620526489674, -2.2139576829964707, -39.38381471815646, 0.727704880103859, 24.65166019895616, 7.33147886140624, 24.394867879588787, 14.31848921666797, -6.202121343589422, -18.671119905316075, -0.6542146822174661, -12.604743381304587, -4.4180558096796565, -41.5669659486563, 5.645479167154948, 3.0782140357831222, 9.0715979974226, 20.684195395377625, 4.981918825082804, 24.368291277785605, 11.32918716428315, -9.356902702240752, 5.147467494249453, -45.70323848150246, -21.239427928498987, 11.952028752532874, 14.55528331469199, -18.506498513752597, -16.69396020901646, -26.31297854512189, 0.8429897768801826, 30.11533244262273, 11.253284418764395, 11.91242942239322, -21.20097618795649, -10.881397121226623, -16.31616196132101, 0.5430333626354413, -27.91193265218885, -23.118627495375875, -15.554492347483286, -17.813879590949515, -17.484196207897952, 6.344805473146368, -4.174313992077831, 9.022537617342328, -24.987663486543966, 21.079381944379772, 22.52747550076394, -0.28013481973661225, -7.1490444166999065, -2.38854830883361, 0.10655132845307325, -1.8914026921450198, 7.298687011059865, -7.502905756169577, 20.076434801750434, -4.63634483865355, -11.141142452060198, -15.197197914166498, -5.442201264246462, 17.773098783399092, 34.342809963054776, -11.880090294190733, -8.207439155125094, -18.41582270672763, -0.156798731204562, 24.696106352334432, -8.777207796398372, -15.534802416011718, -16.60531378226458, 6.404731607946056, 0.9915565677351981, -25.469217964446084, -21.070932751725433, 24.994000558033363, -4.411260387162178, -3.169662881948642, 9.267845900312736, 20.103256987801174, -4.542572347848525, -12.13511936317618, -0.5344780999376639, -23.817637379345864, -21.49054729846418, 13.840146891056875, 17.12256195596141, 16.05600312653047, 10.28727821974879, -13.382514322139057, -37.18517891068459, 11.774821761693573, 14.739740837789729, 18.258814498034123, 27.832730027817917, 2.597954682818303, 23.02387305784046, -8.60854296484181, -24.505155475083253, 18.32869612688141, -25.058336890130434, -1.9363631091194247, 12.1958284382366, 6.863881940527954, 36.17364605873413, -27.48893491220467, -6.491492105646477, -4.685801625452195, -8.129233794188963, -25.76719023303714, -20.212314994878803, -14.494709410738935, 6.704064198033585, 0.7644211981740829, 0.7223509137221509, 11.74145699304594, 14.98134751662667, -4.151893677771343, 6.267134441967083, 17.526933742650996, 3.0201624629948185, 28.461598499981562, 12.679221761800381, 25.931327307672305, -49.86217630274116, -3.2598298194758195, 4.112809988573381, 0.12530404528677241, -19.489269561874508, 2.1315892069152342, -25.12534648626119, 2.2618635316832343, -18.89080196324973, -14.006949622167815, 8.577526543486066, 3.181665917995001, 13.970576405316166, -3.6340420701518727, 0.7894152167430892, 15.331596755167265, 1.501595387525172, 13.107131041553556, 18.62064287574851, -12.312364079651504, -16.10295578820088, 10.63856655398504, 6.206822167866928, 15.199664218558876, 1.65592186807101, -16.22031224522471, 1.0909124826005527, -11.540494963338, 12.044712430427015, -30.148741517866732, 16.99717493553133, 6.813147667768657, -7.962097453208133, 8.740014716809776, 14.13591972994826, 23.964580227896725, 21.815926075302006, 12.227485329995435, -2.4612482564989264, -6.122635006693617, -6.28964394460967, 14.47099049911169, -13.214956304074558, -3.5941078064520484, 28.270311326813683, -6.87737320479908, -16.425174024798896, 8.979628511094653, 1.157985755606228, -8.675189961202783, 13.736453490532728, -0.9644718483912964, 2.6537839758436395, 0.9779132931896537, -0.8581788775424632, -30.866111088469964, 3.978806254409186, 21.49804472138842, 20.98168279837481, -22.84322686952578, 15.808973640677236, -9.42144631638376, 3.5056128873525996, -10.795718596266527, -3.0415332253026524, 13.024596213235522, -0.23747835147599972, 0.10802359514232984, 12.41160219468788, -1.6319366828045752, 0.8417383479807574, 16.93671842566293, 4.527511812230314, -4.99455966167369, -20.69435149900401, 6.66287286192217, -21.68012802854484, -1.1875204703674767, 16.221071186260016, 10.951379694527741, -41.42577160421901, -1.3918729218742927, 4.169961024768868, -27.92691321305809, 1.0303833244378424, 2.0991458145112736, 2.173591536326027, -2.767187868319035, 9.992923752788123, -3.276754990401861, 0.8801013244407905, -6.076921909338129, 12.872735847319504, 21.13605963656332, -35.75577644856725, 13.206092657698287, 20.1746380832699, 25.360652261053644, 9.805695888276782, -13.860967866158461, 11.449712605213017, -20.6465423938865, -3.1444774923498118, 18.06465725635855, -10.071390199281087, -25.635205520920117, 13.589279821889878, 28.610035677621674, 22.918811965753566, 16.48623422060114, 12.625337840615199, 6.4789509492744894, 13.398802752912715, 18.783414075183764, -8.420357104648268, 4.992279491643696, 0.7857769063423492, 3.1835424671684547, -4.920583221201843, 4.368781205936179, 8.456575922499145, -6.007823940880513, -7.808198704781505, 18.36735964723843, -5.211023057765481, -4.912485096951445, 3.5188436598058317, -4.930148899752673, 27.466099026438243, -16.54780504520008, 12.132649965196782, 9.077935070465118, -20.893377172041966, 21.40371922278436, 14.471365410568115, -14.13919945813562, -38.469439867032676, 16.8331746759929, 1.3288162286602518, -13.460693968991787, -23.785059382441464, 18.422118405434055, -40.20250833177791, 6.714241326095543, -8.373865555595508, 18.977238626827514, -2.414575147903019, 0.27894937468566194, -9.141622667930639, 7.4095410059195785, 3.8035098067133664, -2.284306317041537, 12.563682108818531, -11.918819491774702, 0.9119052573817754, -6.600097588239178, -2.147346333801292, 24.302994435413936, 3.749854933571589, -25.584070275779005, 10.124685317758885, 24.15972117721378, -7.1345422222277115, 31.59216132077135, 12.982119227876861, 23.271126312547125, 24.006074525649726, 19.671411712492905, 0.3419660448847699, 16.918566595445473, 11.055197238451033, 14.628725048337515, 19.998132256710246, -24.636138619509833, 9.297420375586114, -0.7283787777005739, -2.512568537887733, 9.525112986113573, 3.304553263630372, 22.423455620122702, -29.59605728409314, -23.83515035304679, -26.999351074983597, 18.037163959038704, -9.601562378014897, 9.586237634178636, 0.2792431381313141, -11.2956271095997, 10.326590276002577, 6.470444381391386, -1.5660261555757717, 5.4930696161037655, 7.973897527087907, -22.26157238362006, 10.71253025199916, 23.747802796600244, -30.67020263770117, 0.009381624300967673, 4.340933451712072, 7.5861504529625226, -26.89986582548332, 26.434073384653633, -22.11842209643604, 5.068628722681591, 2.8173293679164813, -0.23604216321789906, -20.181227872226675, 5.333534549457861, -3.6003214401605548, -4.893112950527308, 18.304262137587514, -12.100508481704384, 6.793889746925068, -14.118850548964668, -2.1919538299528973, -10.125655312820333, -5.481477807904474, -13.078468420930065, 15.975893520438909, 4.95075855157335, -7.943600084197973, -4.494582067136169, 9.25073526954089, -15.80321844391107, -10.371312894629185, -11.096317833774748, -8.09859692675252, 12.882925595569782, 9.640062453954592, 17.10287225293595, 14.906717152889476, 26.969571046841157, -4.911625205974793, -18.852122860972237, 10.209540121654607, 24.36898324958021, 3.969573563318189, 5.636112221428219, -15.994075011545675, -2.428681766142563, -4.8196773770107155, 19.216862457298976, -6.951286271746098, 19.746113781464125, -2.726505040280286, 11.676673751893894],
            type: 'histogram',
            nbinsx: 30,
            name: 'Returns',
            marker: {color: '#e74c3c'}
        }];

        Plotly.newPlot('returns-distribution', returnsData, {
            title: 'Distribution of Returns (1000 Cycles)',
            xaxis: {title: 'Return (%)'},
            yaxis: {title: 'Frequency'}
        });

        // Performance Categories Pie Chart
        var categoryData = [{
            values: [47,
                    198,
                    402,
                    353],
            labels: ['Excellent (0.8+)', 'Good (0.6-0.8)', 'Acceptable (0.4-0.6)', 'Poor (<0.4)'],
            type: 'pie',
            marker: {colors: ['#27ae60', '#f39c12', '#e67e22', '#e74c3c']}
        }];

        Plotly.newPlot('performance-categories', categoryData, {
            title: 'Performance Category Distribution'
        });

        // Equity Curve
        var equityData = [{
            y: [300, 301.14331514068357, 296.15654011578414, 313.9329851196335, 305.41894893080206, 313.0174050515077, 312.7466241834978, 316.0433174162496, 320.340920016702, 316.51989919199525, 329.0270121313258, 317.10247291456363, 322.30721846982055, 336.46214687168543, 335.45002380552233, 347.7019554884072, 337.84175221826564, 348.5021464822955, 333.21260608668297, 337.932425134014, 351.97762768708486, 341.29816087192756, 351.51316196495253, 360.8360454182358, 360.0058563224433, 360.0554761015979, 360.25446265585344, 366.01112937295983, 370.38624186187764, 366.6955283150691, 374.35868014006024, 380.3921537209936, 376.38760933350073, 380.34061425070325, 384.9188941700139, 387.7566710804487, 391.8922678649922, 384.92812522009024, 389.85538748807346, 392.5406065397517, 392.98043917418664, 391.0079303932109, 386.2276052935115, 410.9772635587649, 399.73053497776226, 417.83254563352955, 416.19704744308234, 412.9715581009687, 420.58211052407364, 422.1256662335686, 416.99112818530114, 424.92511113185185, 422.6008061372925, 433.27064001085546, 428.92389524100304, 434.51685231901666, 436.80100540494624, 440.6078950026969, 450.8127754055701, 443.7951970857204, 453.5896357098726, 452.39998497167517, 459.5920127301829, 453.3078210759259, 460.98068120984783, 459.7036598869983, 447.55303489418395, 459.5507820748976, 470.41188472976376, 472.5813245814, 466.0251130568254, 476.10183774419, 480.0163011326275, 479.826672151312, 486.1440931882982, 487.96277653964387, 487.32428210297644, 487.44587590567943, 497.7127079604625, 497.3158104893941, 490.59283673734853, 501.10448828040694, 496.7448764527113, 500.1771603378541, 504.2404498230718, 510.2243661782985, 514.3724278879564, 512.9552905537936, 515.6740869185313, 517.4837817234916, 527.7375681854381, 510.7352824820017, 525.9566129516261, 537.7249239769118, 535.0861502513869, 541.3342362028357, 527.3287557024454, 533.7887538426544, 545.0702407034323, 552.7531529662883, 541.7938189157763, 548.0611910864674, 554.507320709335, 547.7375080145615, 560.1084562979049, 546.6215767548188, 564.9611973223243, 560.0245751605607, 565.2620140474179, 573.2594197396849, 574.7362607774612, 574.9600939782415, 582.3452027581767, 571.4991199369059, 583.5220718575071, 581.0491493457076, 592.0901573433999, 593.4353353481806, 596.11674109432, 603.2269091635357, 603.1317376621749, 599.2128933311329, 602.4936607325625, 608.0046919035586, 608.1808733737391, 613.5258666095486, 601.3483978761349, 624.4040175939666, 617.8051429177816, 611.8876733455372, 617.9310332420824, 614.3533647441539, 622.0488245990713, 629.2887578568011, 640.0735730624721, 637.053372927099, 638.6643030598228, 634.3244773939215, 638.3775734717325, 641.2653615331391, 643.9693569390695, 648.242315618757, 651.1199446914176, 653.3046081318657, 654.1433788486576, 660.6752658290611, 660.9279260831661, 657.0221637688888, 667.6719091112897, 669.4161800110921, 664.6018627372241, 672.0434927665173, 680.4853628050568, 674.3177503066964, 676.2891046513575, 683.3755818900413, 690.6146613490598, 699.0516379536135, 695.7946767742684, 700.4241387007805, 699.2334881425083, 696.5744201017864, 705.7585553829846, 704.5719597553594, 709.38516918914, 705.2698508682452, 714.832229004199, 714.3974590161705, 718.6562887359204, 719.5746341258744, 721.1139844232916, 727.423453898092, 725.4695443555422, 722.056559348112, 723.1301496215877, 735.9220828471349, 735.2639109263557, 731.3545041128398, 743.032674835269, 744.5306272251332, 745.0019320338863, 751.2340465909043, 759.075470050539, 761.4229535934257, 758.2188232155429, 759.5764478356857, 763.2886760380584, 766.5450346638718, 766.1585091147098, 770.8727152997656, 764.631852144791, 775.4067444167338, 779.884067975123, 777.550790289968, 782.0226367785696, 779.4424730964944, 781.2484320852034, 791.0540753897959, 788.852184921312, 786.1158716921199, 791.9411085043278, 795.1681045310396, 806.3729024886587, 805.8955700568423, 803.1419890092135, 807.0611704751442, 819.0604220805361, 819.6805121759847, 811.9096978600909, 825.8176292981316, 822.0968559577509, 828.9980581302813, 826.6834602249925, 835.9291459937109, 826.701896942301, 833.7227478430924, 843.5822988894956, 828.0369592590137, 842.2696929793776, 843.032268400634, 850.9233061527187, 855.3495671770548, 856.1514190965348, 856.2050012669862, 857.7028227404768, 858.2864468926006, 863.5498631076232, 872.2982741479276, 863.3474385086123, 867.9895926243796, 872.122450137166, 880.0046633959784, 878.616511697761, 884.917409477726, 887.6705499525181, 887.9601841291, 882.1800319171722, 898.3054695594835, 899.5619033554635, 895.3177002525117, 890.5911487875823, 892.1975092106283, 903.7715781327686, 901.4912477893461, 898.9140543331981, 902.103171503185, 910.15008713335, 915.9746478690549, 924.5285652550791, 922.4211038717342, 920.5929773476429, 926.899747788364, 928.9528379279433, 933.6977260359797, 932.3002894320807, 940.1809887550605, 941.6222590011374, 940.4135000181928, 941.9038782186705, 949.5987423949522, 943.9326284094648, 942.4669358542831, 944.93851004158, 951.0181276245524, 960.7982614938862, 957.8860057269507, 963.5225800140561, 962.5235196455947, 965.0632131700802, 964.8726816274442, 972.0405218729215, 974.1241813211052, 979.5633251159092, 981.4384880350586, 981.514263095208, 977.4695557746991, 992.8528973378914, 989.5747783048099, 992.8982134494695, 1003.0024753290553, 1002.0411106329798, 1002.2322459940837, 1002.4884710631459, 1004.9417722558293, 1008.7005724886093, 1012.0345336292831, 1021.0570087789207, 1027.366330413814, 1018.9513040270177, 1009.2336196397819, 1017.7412776335414, 1034.057484409359, 1031.0932631412788, 1032.130232436225, 1031.6658104429762, 1029.05488970387, 1045.8889487737274, 1035.8839487426296, 1046.1465590826913, 1038.4964911512293, 1046.4232565758587, 1054.9647856522647, 1051.251529137666, 1053.15775062317, 1057.5531813417433, 1053.3199855378255, 1054.87926546477, 1068.5711824134696, 1065.388950371911, 1073.5324238029073, 1078.0909150857535, 1080.9932130449772, 1069.3949585771184, 1076.5108535587306, 1085.187926535219, 1087.7650083595183, 1098.7471879209816, 1085.4619323338345, 1098.3408420072706, 1096.8613730501927, 1089.2988206666826, 1097.260923816367, 1103.8126400616165, 1096.4781664585187, 1097.1304699751418, 1101.1937871068167, 1113.7956682754218, 1112.5766637063327, 1114.400221513432, 1117.0718908241713, 1115.3793879417103, 1122.7179840857996, 1118.398009759641, 1135.9382056967945, 1140.961648603386, 1123.3696814330542, 1135.1543517033795, 1137.979942846826, 1141.6275774669148, 1150.007652121872, 1145.1689460890304, 1149.5587426863435, 1167.395274819007, 1154.4435354619734, 1166.331199301497, 1162.5099382869441, 1159.7314550346207, 1161.3963300641228, 1167.4636370250014, 1164.3676343762672, 1168.1806701680216, 1172.884696994871, 1178.9667177096362, 1189.1299860587053, 1180.0752278660086, 1187.3075902318906, 1179.3605107670896, 1195.3014432044365, 1183.8915871253653, 1198.9919323605072, 1197.095375233591],
            type: 'scatter',
            mode: 'lines',
            name: 'Equity Curve',
            line: {color: '#2ecc71', width: 2}
        }];

        Plotly.newPlot('equity-curve', equityData, {
            title: 'Equity Curve (Best Cycle)',
            xaxis: {title: 'Time (Hours)'},
            yaxis: {title: 'Balance ($)'}
        });

        // Drawdown Chart
        var drawdownData = [{
            y: [0.4166541352548741, 1.1173002422222522, 2.14754932494831, 0.6125234196642783, 0.024286736913700417, 1.463515595060902, 1.8615882456472899, 6.586686631071672, 1.7806164547786552, 1.2741861990818135, 1.7038245684908155, 5.259419941359673, 3.524923486807359, 0.7801051870225187, 0.5416613527100657, 0.6093407529824687, 1.4040571662905599, 2.7255420440474283, 1.3992128218234166, 0.14040805748686422, 2.571672729558887, 3.279486311697933, 0.6835666533837763, 2.18203682623164, 1.1123152024141458, 4.398601866036979, 3.731572871710572, 1.4907007177838723, 2.352102825007216, 4.2971494349683095, 1.369235361092421, 0.7515720645165179, 2.802135000387706, 0.49709260987586884, 0.9837825244043833, 4.01576400792924, 2.5777716072238777, 1.0343951137892249, 0.4834922790321473, 0.8800149430473078, 0.6684411648332937, 0.38738622819821056, 2.719950834690748, 0.5808914739810833, 1.210886144410004, 0.9750629261681788, 8.043383201279264, 0.9921769819367778, 0.16672513736401579, 0.18823843422262246, 1.7868763117537063, 1.829401751422319, 1.0851795793944694, 1.130717417054398, 0.11088289394723155, 1.4536003278733125, 1.4053426850085895, 0.32397771309769735, 0.21927125099824155, 0.2638025653222651, 0.21300732981084178, 1.8817212061210158, 0.43751300799540865, 0.5791815248508042, 0.3812853085608323, 6.332344012779187, 3.8567736506075048, 0.5240152332617387, 0.7937035013763382, 0.4711734001356005, 1.3540593738392566, 0.0396107408473615, 2.2256334447126176, 1.4811407880987093, 4.578132339663227, 0.3591929886914543, 3.2603963060146435, 3.140059572790458, 0.27205458493844736, 1.0168544183399473, 0.3730270001949683, 0.5879919275782174, 0.6110029308548264, 1.6249265546243796, 1.0600540316661806, 5.902772664709677, 0.2701969928026824, 7.275774658761936, 0.5243890223098862, 2.964500138625988, 0.8279118853923453, 0.30882468408939373, 3.90229628026705, 0.7388283438162697, 2.1508977141009273, 1.6714671673416528, 1.2763951330119008, 1.3696417717115903, 2.635312863790731, 0.1837326444618298, 2.206329241399237, 4.52311858533992, 0.11905037150284849, 0.3031657504675039, 2.522017298663959, 1.1499520786556345, 1.033515101863492, 0.29460385294506536, 1.4135595296891874, 0.27059804780663, 1.294896746325118, 2.8845894239285186, 0.5975247535452334, 1.3108789466142836, 9.660623813837312, 2.3208717067408133, 3.682194885280652, 1.0717659281380554, 2.9208372352813115, 0.08270504741304703, 0.26229403511135396, 0.4539783548294418, 1.0281682692857301, 3.7382948260502085, 0.8501096315577401, 2.1262269489809213, 2.471025292713935, 1.764659742008248, 0.8839738737479477, 1.7210184224585323, 0.8379081311970465, 0.1752021675966949, 9.868453862988218, 0.2720574822557925, 0.15019806059542123, 4.2161882562518835, 0.7185986575174549, 2.1013419640583795, 0.7979893620736549, 0.9933664373414067, 0.6079290894258735, 0.8402764852776339, 1.0576411338104919, 2.5489054839840177, 1.432979805687322, 5.749339186990142, 0.0855653178984613, 1.3986054648399004, 1.7552210450705996, 5.056388013493555, 0.10081584367300253, 1.7042838777252753, 3.8432342563786506, 0.5539531047914477, 4.179668292790172, 0.03845414084119501, 2.6369322326266356, 5.409507914435743, 2.1412025418568006, 1.1561730062511242, 0.09183871236236561, 5.054304647583977, 1.270874678847238, 0.0588509155966726, 2.3412119175010804, 1.314931807564714, 0.2655669275116374, 0.48228871946398916, 0.13332834243607544, 1.708010461145451, 1.9458781476858114, 1.4093964691301928, 3.3976547715561205, 3.9466896210257287, 1.9122701416354255, 0.02567708445515775, 2.376393511326946, 1.0825669715078534, 1.6673832808855487, 2.094397434535899, 0.8622940088124355, 4.980204370600261, 0.28768269722389717, 2.2265087694405885, 0.008738221270406207, 5.171252048583411, 0.23870282962618283, 0.19938484194945397, 0.8099533539768657, 0.9354246279543846, 0.48236066169867814, 1.7595399211828782, 1.170071656800162, 0.06954646097062675, 0.7703018770361735, 3.326024598051478, 2.340441307841068, 2.581029336416415, 2.3954310048295873, 0.7449497050841543, 0.20997433089399437, 0.636192346191062, 3.565028280811263, 7.871688768070963, 1.3618326259658908, 0.019495419708923593, 0.3017499964500638, 0.7774536815068964, 1.728276019617295, 0.15612557060628668, 5.741984276929747, 0.9085241154470971, 4.874158879358901, 3.0330047611790936, 2.816566897686181, 1.8664647682197992, 1.97119489655971, 2.0322400085155214, 1.981605039700313, 1.613654331795817, 8.5974335773007, 2.635706186921922, 3.000686251240387, 4.118694621003291, 0.48577375568834474, 0.1750502740908634, 1.319016481575418, 1.4434254184734925, 3.534310657101452, 1.1731956291460113, 1.6919116418570812, 1.497619754906486, 0.07790328892638976, 1.9101405470197588, 3.5210249906379163, 0.34276453458182116, 1.8044390640160655, 1.800886658069636, 0.35869587849564033, 0.2635540334119882, 7.82924706538841, 1.000823967685835, 0.516043906604458, 0.3758533791744234, 2.202842742396706, 1.8803744716885142, 2.0631345147109768, 0.18424379608616087, 4.479870148505925, 0.9861888324217872, 0.7519176817660067, 1.2411375592712244, 0.9888092643688902, 3.983989898194645, 5.297515761928237, 0.7232406370543437, 3.9892570012679975, 3.0421502520724197, 5.341730589078804, 0.5640636296909114, 1.7205730655612013, 0.6419113247160699, 3.475616611733151, 0.010676829167432813, 1.663987233554991, 0.8286184987832637, 0.7792338347113545, 2.070747295758262, 3.8141312155953817, 1.879095132266221, 3.8370002211527123, 0.6146735656928114, 1.4658638213040829, 1.1976260049337542, 0.023426464698819406, 1.6275230854153495, 2.1666060956830515, 1.7925566669819075, 2.665733921867834, 1.6941802300498914, 0.13816486853249113, 0.2476046598191091, 1.815031576818308, 0.6431098476266134, 0.16599190800687852, 1.1228444391720414, 1.6163198055376649, 0.7972153135779959, 2.5490316263517494, 2.0643607927484715, 0.8093858958761645, 2.8119538271445403, 2.0922012160675343, 2.029160273279034, 1.612307934889675, 0.0229100542208668, 2.3847946750996165, 0.9433437715736734, 5.7157603129879755, 3.4073298400119247, 1.7719812513176287, 0.5811912436199945, 0.45560224515297676, 0.8321974028269089, 1.2946960885159748, 0.7021399623729702, 1.4469220565702356, 0.36153455186064987, 7.607486663616747, 0.22840914068625257, 5.374852870723002, 4.897026275794334, 1.4903993443169223, 2.5813159006045385, 1.145573331708217, 1.988691461056508, 0.8327769147665054, 0.7166641174920212, 1.7265892604774167, 1.378827015534065, 1.7327945871185788, 0.67559585752939, 0.9144364853077677, 0.17525504763765445, 0.10738470232703318, 0.5846044704329965, 0.1542953332531426, 0.2191269127147829, 3.352527753086699, 0.07929889989024638, 2.1514101120930937, 0.6364413258044345, 4.104363859482238, 1.327146794055415, 0.729346430946071, 1.4581246985679108, 0.9623977869682929, 1.6616075788927442, 0.24327988165824438, 11.611964523068961, 1.108704899206723, 0.85612904303874, 2.4771964656418146, 0.9047971245221563, 3.5828107242078593, 1.2112645167291958, 2.5427796452827733, 0.22735269629927832, 3.743153816769352, 2.661477028176835, 0.47579567100806003, 0.3139767606606616, 0.7604497262860668, 7.46529373357045, 0.5936651540032144, 1.2180797988929064, 0.06096011791416974, 1.0124309655175967, 1.694089190277588, 6.12052505645797, 1.4543845675879286],
            type: 'scatter',
            mode: 'lines',
            fill: 'tozeroy',
            name: 'Drawdown',
            line: {color: '#e74c3c'},
            fillcolor: 'rgba(231, 76, 60, 0.3)'
        }];

        Plotly.newPlot('drawdown-chart', drawdownData, {
            title: 'Drawdown Chart (Best Cycle)',
            xaxis: {title: 'Time (Hours)'},
            yaxis: {title: 'Drawdown (%)', range: [Math.min(...[0.4166541352548741, 1.1173002422222522, 2.14754932494831, 0.6125234196642783, 0.024286736913700417, 1.463515595060902, 1.8615882456472899, 6.586686631071672, 1.7806164547786552, 1.2741861990818135, 1.7038245684908155, 5.259419941359673, 3.524923486807359, 0.7801051870225187, 0.5416613527100657, 0.6093407529824687, 1.4040571662905599, 2.7255420440474283, 1.3992128218234166, 0.14040805748686422, 2.571672729558887, 3.279486311697933, 0.6835666533837763, 2.18203682623164, 1.1123152024141458, 4.398601866036979, 3.731572871710572, 1.4907007177838723, 2.352102825007216, 4.2971494349683095, 1.369235361092421, 0.7515720645165179, 2.802135000387706, 0.49709260987586884, 0.9837825244043833, 4.01576400792924, 2.5777716072238777, 1.0343951137892249, 0.4834922790321473, 0.8800149430473078, 0.6684411648332937, 0.38738622819821056, 2.719950834690748, 0.5808914739810833, 1.210886144410004, 0.9750629261681788, 8.043383201279264, 0.9921769819367778, 0.16672513736401579, 0.18823843422262246, 1.7868763117537063, 1.829401751422319, 1.0851795793944694, 1.130717417054398, 0.11088289394723155, 1.4536003278733125, 1.4053426850085895, 0.32397771309769735, 0.21927125099824155, 0.2638025653222651, 0.21300732981084178, 1.8817212061210158, 0.43751300799540865, 0.5791815248508042, 0.3812853085608323, 6.332344012779187, 3.8567736506075048, 0.5240152332617387, 0.7937035013763382, 0.4711734001356005, 1.3540593738392566, 0.0396107408473615, 2.2256334447126176, 1.4811407880987093, 4.578132339663227, 0.3591929886914543, 3.2603963060146435, 3.140059572790458, 0.27205458493844736, 1.0168544183399473, 0.3730270001949683, 0.5879919275782174, 0.6110029308548264, 1.6249265546243796, 1.0600540316661806, 5.902772664709677, 0.2701969928026824, 7.275774658761936, 0.5243890223098862, 2.964500138625988, 0.8279118853923453, 0.30882468408939373, 3.90229628026705, 0.7388283438162697, 2.1508977141009273, 1.6714671673416528, 1.2763951330119008, 1.3696417717115903, 2.635312863790731, 0.1837326444618298, 2.206329241399237, 4.52311858533992, 0.11905037150284849, 0.3031657504675039, 2.522017298663959, 1.1499520786556345, 1.033515101863492, 0.29460385294506536, 1.4135595296891874, 0.27059804780663, 1.294896746325118, 2.8845894239285186, 0.5975247535452334, 1.3108789466142836, 9.660623813837312, 2.3208717067408133, 3.682194885280652, 1.0717659281380554, 2.9208372352813115, 0.08270504741304703, 0.26229403511135396, 0.4539783548294418, 1.0281682692857301, 3.7382948260502085, 0.8501096315577401, 2.1262269489809213, 2.471025292713935, 1.764659742008248, 0.8839738737479477, 1.7210184224585323, 0.8379081311970465, 0.1752021675966949, 9.868453862988218, 0.2720574822557925, 0.15019806059542123, 4.2161882562518835, 0.7185986575174549, 2.1013419640583795, 0.7979893620736549, 0.9933664373414067, 0.6079290894258735, 0.8402764852776339, 1.0576411338104919, 2.5489054839840177, 1.432979805687322, 5.749339186990142, 0.0855653178984613, 1.3986054648399004, 1.7552210450705996, 5.056388013493555, 0.10081584367300253, 1.7042838777252753, 3.8432342563786506, 0.5539531047914477, 4.179668292790172, 0.03845414084119501, 2.6369322326266356, 5.409507914435743, 2.1412025418568006, 1.1561730062511242, 0.09183871236236561, 5.054304647583977, 1.270874678847238, 0.0588509155966726, 2.3412119175010804, 1.314931807564714, 0.2655669275116374, 0.48228871946398916, 0.13332834243607544, 1.708010461145451, 1.9458781476858114, 1.4093964691301928, 3.3976547715561205, 3.9466896210257287, 1.9122701416354255, 0.02567708445515775, 2.376393511326946, 1.0825669715078534, 1.6673832808855487, 2.094397434535899, 0.8622940088124355, 4.980204370600261, 0.28768269722389717, 2.2265087694405885, 0.008738221270406207, 5.171252048583411, 0.23870282962618283, 0.19938484194945397, 0.8099533539768657, 0.9354246279543846, 0.48236066169867814, 1.7595399211828782, 1.170071656800162, 0.06954646097062675, 0.7703018770361735, 3.326024598051478, 2.340441307841068, 2.581029336416415, 2.3954310048295873, 0.7449497050841543, 0.20997433089399437, 0.636192346191062, 3.565028280811263, 7.871688768070963, 1.3618326259658908, 0.019495419708923593, 0.3017499964500638, 0.7774536815068964, 1.728276019617295, 0.15612557060628668, 5.741984276929747, 0.9085241154470971, 4.874158879358901, 3.0330047611790936, 2.816566897686181, 1.8664647682197992, 1.97119489655971, 2.0322400085155214, 1.981605039700313, 1.613654331795817, 8.5974335773007, 2.635706186921922, 3.000686251240387, 4.118694621003291, 0.48577375568834474, 0.1750502740908634, 1.319016481575418, 1.4434254184734925, 3.534310657101452, 1.1731956291460113, 1.6919116418570812, 1.497619754906486, 0.07790328892638976, 1.9101405470197588, 3.5210249906379163, 0.34276453458182116, 1.8044390640160655, 1.800886658069636, 0.35869587849564033, 0.2635540334119882, 7.82924706538841, 1.000823967685835, 0.516043906604458, 0.3758533791744234, 2.202842742396706, 1.8803744716885142, 2.0631345147109768, 0.18424379608616087, 4.479870148505925, 0.9861888324217872, 0.7519176817660067, 1.2411375592712244, 0.9888092643688902, 3.983989898194645, 5.297515761928237, 0.7232406370543437, 3.9892570012679975, 3.0421502520724197, 5.341730589078804, 0.5640636296909114, 1.7205730655612013, 0.6419113247160699, 3.475616611733151, 0.010676829167432813, 1.663987233554991, 0.8286184987832637, 0.7792338347113545, 2.070747295758262, 3.8141312155953817, 1.879095132266221, 3.8370002211527123, 0.6146735656928114, 1.4658638213040829, 1.1976260049337542, 0.023426464698819406, 1.6275230854153495, 2.1666060956830515, 1.7925566669819075, 2.665733921867834, 1.6941802300498914, 0.13816486853249113, 0.2476046598191091, 1.815031576818308, 0.6431098476266134, 0.16599190800687852, 1.1228444391720414, 1.6163198055376649, 0.7972153135779959, 2.5490316263517494, 2.0643607927484715, 0.8093858958761645, 2.8119538271445403, 2.0922012160675343, 2.029160273279034, 1.612307934889675, 0.0229100542208668, 2.3847946750996165, 0.9433437715736734, 5.7157603129879755, 3.4073298400119247, 1.7719812513176287, 0.5811912436199945, 0.45560224515297676, 0.8321974028269089, 1.2946960885159748, 0.7021399623729702, 1.4469220565702356, 0.36153455186064987, 7.607486663616747, 0.22840914068625257, 5.374852870723002, 4.897026275794334, 1.4903993443169223, 2.5813159006045385, 1.145573331708217, 1.988691461056508, 0.8327769147665054, 0.7166641174920212, 1.7265892604774167, 1.378827015534065, 1.7327945871185788, 0.67559585752939, 0.9144364853077677, 0.17525504763765445, 0.10738470232703318, 0.5846044704329965, 0.1542953332531426, 0.2191269127147829, 3.352527753086699, 0.07929889989024638, 2.1514101120930937, 0.6364413258044345, 4.104363859482238, 1.327146794055415, 0.729346430946071, 1.4581246985679108, 0.9623977869682929, 1.6616075788927442, 0.24327988165824438, 11.611964523068961, 1.108704899206723, 0.85612904303874, 2.4771964656418146, 0.9047971245221563, 3.5828107242078593, 1.2112645167291958, 2.5427796452827733, 0.22735269629927832, 3.743153816769352, 2.661477028176835, 0.47579567100806003, 0.3139767606606616, 0.7604497262860668, 7.46529373357045, 0.5936651540032144, 1.2180797988929064, 0.06096011791416974, 1.0124309655175967, 1.694089190277588, 6.12052505645797, 1.4543845675879286]) - 1, 1]}
        });
        
            </script>
        </body>
        </html>
        