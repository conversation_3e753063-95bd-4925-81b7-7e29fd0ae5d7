"""
Configuration settings for the trading system.
"""
import os
from pathlib import Path
from typing import Optional

from pydantic import BaseSettings, Field, validator
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
env_path = Path(__file__).parent.parent.parent / ".env"
if env_path.exists():
    load_dotenv(env_path)


class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    DEBUG: bool = Field(False, env="DEBUG")
    LOG_LEVEL: str = Field("INFO", env="LOG_LEVEL")
    
    # Binance API settings
    BINANCE_API_KEY: Optional[str] = Field(None, env="BINANCE_API_KEY")
    BINANCE_API_SECRET: Optional[str] = Field(None, env="BINANCE_API_SECRET")
    BINANCE_API_URL: str = Field("https://api.binance.com", env="BINANCE_API_URL")
    
    # Data settings
    DATA_DIR: Path = Field(Path("data"), env="DATA_DIR")
    CACHE_DIR: Path = Field(Path("data/cache"), env="CACHE_DIR")
    
    # Trading settings
    DEFAULT_SYMBOL: str = Field("BTCUSDT", env="DEFAULT_SYMBOL")
    DEFAULT_INTERVAL: str = Field("1h", env="DEFAULT_INTERVAL")
    RISK_PER_TRADE: float = Field(0.05, env="RISK_PER_TRADE")  # 5% risk per trade
    
    # Grid settings
    GRID_SPACING: float = Field(0.0025, env="GRID_SPACING")  # 0.25%
    TAKE_PROFIT_MULTIPLIER: float = Field(2.0, env="TAKE_PROFIT_MULTIPLIER")  # 2:1 risk-reward
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    @validator("DATA_DIR", "CACHE_DIR", pre=True)
    def ensure_paths_exist(cls, v):
        """Ensure data and cache directories exist."""
        path = Path(v)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @validator("RISK_PER_TRADE")
    def validate_risk(cls, v):
        """Validate risk per trade is between 0 and 1."""
        if not 0 < v <= 1:
            raise ValueError("RISK_PER_TRADE must be between 0 and 1")
        return v


# Create settings instance
settings = Settings()

# Configure logging
import logging

logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)
