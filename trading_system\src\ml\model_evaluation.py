"""
Model Evaluation and Testing with Real Market Data
Tests trained TCN-PPO model against baseline strategies
"""
import asyncio
import sys
import numpy as np
import torch
import matplotlib.pyplot as plt
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List, Tuple
import logging

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add parent directories to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.ml.integrated_training import SimpleTCNPPO, TradingFeatureExtractor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TradingStrategy:
    """Base class for trading strategies."""

    def __init__(self, name: str):
        self.name = name

    def get_action(self, candles: List, current_idx: int) -> Action:
        """Get trading action given market data."""
        raise NotImplementedError


class RandomStrategy(TradingStrategy):
    """Random trading strategy for baseline comparison."""

    def __init__(self):
        super().__init__("Random")

    def get_action(self, candles: List, current_idx: int) -> Action:
        return np.random.choice([Action.BUY, Action.SELL, Action.HOLD])


class SimpleMovingAverageStrategy(TradingStrategy):
    """Simple moving average crossover strategy."""

    def __init__(self, short_window: int = 5, long_window: int = 20):
        super().__init__(f"SMA_{short_window}_{long_window}")
        self.short_window = short_window
        self.long_window = long_window

    def get_action(self, candles: List, current_idx: int) -> Action:
        if current_idx < self.long_window:
            return Action.HOLD

        # Calculate moving averages
        recent_candles = candles[current_idx - self.long_window:current_idx]
        short_ma = np.mean([c.close for c in recent_candles[-self.short_window:]])
        long_ma = np.mean([c.close for c in recent_candles])

        # Trading logic
        if short_ma > long_ma * 1.001:  # 0.1% threshold
            return Action.BUY
        elif short_ma < long_ma * 0.999:
            return Action.SELL
        else:
            return Action.HOLD


class MLStrategy(TradingStrategy):
    """ML-based trading strategy using trained TCN-PPO model."""

    def __init__(self, model_path: str):
        super().__init__("TCN-PPO")
        self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Load trained model
        feature_dim = self.feature_extractor.get_feature_dim()
        self.model = SimpleTCNPPO(feature_dim).to(self.device)

        if Path(model_path).exists():
            checkpoint = torch.load(model_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.model.eval()
            logger.info(f"Loaded trained model from {model_path}")
        else:
            logger.warning(f"Model file {model_path} not found, using random weights")

    def get_action(self, candles: List, current_idx: int) -> Action:
        if current_idx < self.feature_extractor.lookback_window:
            return Action.HOLD

        try:
            # Extract features
            features = self.feature_extractor.extract_features(candles[:current_idx+1])
            state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

            # Get action from model
            with torch.no_grad():
                action_idx, _, _ = self.model.act(state_tensor, deterministic=True)

            # Convert to environment action
            action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
            return action_map[action_idx]

        except Exception as e:
            logger.warning(f"Error in ML strategy: {e}")
            return Action.HOLD


class StrategyEvaluator:
    """Evaluate and compare different trading strategies."""

    def __init__(self, initial_balance: float = 300.0):
        self.initial_balance = initial_balance

    async def get_test_data(self, days: int = 30) -> List:
        """Get fresh test data for evaluation."""
        logger.info(f"Fetching {days} days of test data...")

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch test data: {response.error}")

            logger.info(f"Fetched {len(response.data)} test candles")
            return response.data

    def evaluate_strategy(self, strategy: TradingStrategy, candles: List) -> Dict:
        """Evaluate a single strategy on the given data."""
        logger.info(f"Evaluating {strategy.name} strategy...")

        env = GridTradingEnv(
            initial_balance=self.initial_balance,
            risk_per_trade=0.05,
            grid_spacing=0.0025,
            take_profit_multiplier=2.0,
            fee_rate=0.001
        )

        # Reset environment
        env.reset(candles[0].close, candles[0].timestamp)

        # Track performance
        equity_curve = [(candles[0].timestamp, env.equity)]
        actions_taken = []

        for i in range(1, len(candles)):
            candle = candles[i]

            # Get action from strategy
            action = strategy.get_action(candles, i)

            # Execute action
            state, reward, done, info = env.step(action, candle.close, candle.timestamp, candle)

            # Track equity
            equity_curve.append((candle.timestamp, env.equity))

            # Track actions
            if action != Action.HOLD:
                actions_taken.append({
                    'timestamp': candle.timestamp,
                    'action': action.name,
                    'price': candle.close,
                    'balance': env.balance
                })

        # Get final metrics
        metrics = env.get_metrics()

        return {
            'strategy': strategy.name,
            'metrics': metrics,
            'equity_curve': equity_curve,
            'actions_taken': actions_taken,
            'final_balance': env.balance,
            'total_return': ((env.balance / self.initial_balance) - 1) * 100,
            'total_trades': len(env.closed_positions),
            'win_rate': metrics['win_rate'],
            'max_drawdown': metrics['max_drawdown'] * 100
        }

    async def compare_strategies(self, model_path: str = "models/tcn_ppo_trading.pth") -> Dict:
        """Compare multiple strategies on test data."""
        logger.info("🔬 Starting Strategy Comparison")
        logger.info("=" * 50)

        # Get test data
        test_data = await self.get_test_data(days=14)  # 2 weeks of test data

        # Initialize strategies
        strategies = [
            RandomStrategy(),
            SimpleMovingAverageStrategy(short_window=5, long_window=20),
            MLStrategy(model_path)
        ]

        # Evaluate each strategy
        results = {}
        for strategy in strategies:
            try:
                result = self.evaluate_strategy(strategy, test_data)
                results[strategy.name] = result

                logger.info(f"✅ {strategy.name}:")
                logger.info(f"   Final Balance: ${result['final_balance']:,.2f}")
                logger.info(f"   Total Return: {result['total_return']:+.2f}%")
                logger.info(f"   Total Trades: {result['total_trades']}")
                logger.info(f"   Win Rate: {result['win_rate']:.1f}%")
                logger.info(f"   Max Drawdown: {result['max_drawdown']:.2f}%")
                logger.info("")

            except Exception as e:
                logger.error(f"❌ Error evaluating {strategy.name}: {e}")
                continue

        return results

    def generate_report(self, results: Dict, save_path: str = "reports/strategy_comparison.html"):
        """Generate HTML report comparing strategies."""
        logger.info("📊 Generating comparison report...")

        # Create report directory
        Path(save_path).parent.mkdir(parents=True, exist_ok=True)

        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Trading Strategy Comparison</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .strategy {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
                .metrics {{ display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; }}
                .metric {{ padding: 10px; background: #f5f5f5; text-align: center; }}
                .best {{ background: #d4edda; }}
                .worst {{ background: #f8d7da; }}
            </style>
        </head>
        <body>
            <h1>Trading Strategy Comparison Report</h1>
            <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

            <h2>Summary</h2>
            <table border="1" style="width:100%; border-collapse: collapse;">
                <tr>
                    <th>Strategy</th>
                    <th>Final Balance</th>
                    <th>Total Return</th>
                    <th>Total Trades</th>
                    <th>Win Rate</th>
                    <th>Max Drawdown</th>
                </tr>
        """

        # Find best performing strategy
        best_return = max(results.values(), key=lambda x: x['total_return'])['total_return']

        for strategy_name, result in results.items():
            is_best = result['total_return'] == best_return
            row_class = 'style="background-color: #d4edda;"' if is_best else ''

            html_content += f"""
                <tr {row_class}>
                    <td>{strategy_name}</td>
                    <td>${result['final_balance']:,.2f}</td>
                    <td>{result['total_return']:+.2f}%</td>
                    <td>{result['total_trades']}</td>
                    <td>{result['win_rate']:.1f}%</td>
                    <td>{result['max_drawdown']:.2f}%</td>
                </tr>
            """

        html_content += """
            </table>

            <h2>Key Insights</h2>
            <ul>
        """

        # Add insights
        best_strategy = max(results.items(), key=lambda x: x[1]['total_return'])
        html_content += f"<li>Best performing strategy: <strong>{best_strategy[0]}</strong> with {best_strategy[1]['total_return']:+.2f}% return</li>"

        ml_result = results.get('TCN-PPO')
        if ml_result:
            html_content += f"<li>ML Strategy (TCN-PPO) achieved {ml_result['total_return']:+.2f}% return with {ml_result['win_rate']:.1f}% win rate</li>"

        html_content += """
            </ul>
        </body>
        </html>
        """

        # Save report
        with open(save_path, 'w') as f:
            f.write(html_content)

        logger.info(f"📄 Report saved to {save_path}")


async def main():
    """Main evaluation function."""
    logger.info("🧪 Starting Model Evaluation")
    logger.info("=" * 40)

    # Initialize evaluator
    evaluator = StrategyEvaluator(initial_balance=10000.0)

    # Compare strategies
    results = await evaluator.compare_strategies()

    # Generate report
    evaluator.generate_report(results)

    logger.info("✅ Evaluation completed successfully!")


if __name__ == "__main__":
    asyncio.run(main())
