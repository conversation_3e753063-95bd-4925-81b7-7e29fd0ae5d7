"""
Quick Investigation: Why TCN-CNN-PPO is Not Trading
Fast 50-cycle analysis to identify the core issue
"""
import asyncio
import sys
import numpy as np
import torch
import torch.nn as nn
from datetime import datetime, timedelta, timezone
from pathlib import Path
import logging
from typing import Dict, List

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.ml.integrated_training import TradingFeatureExtractor, SimpleTCNPPO

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class QuickInvestigator:
    """Quick investigation of model behavior."""
    
    def __init__(self):
        self.initial_capital = 300.0
        self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
    async def collect_test_data(self) -> Dict[str, List]:
        """Collect test data for investigation."""
        logger.info("Collecting test data...")
        
        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=180)  # 6 months
            
            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )
            
            if not response.success:
                raise RuntimeError(f"Failed to fetch data: {response.error}")
            
            all_candles = response.data
            
            # Split data: 90 train, 60 val, 30 test
            train_end = len(all_candles) - 90 * 24
            val_end = len(all_candles) - 30 * 24
            
            return {
                'training': all_candles[:train_end],
                'validation': all_candles[train_end:val_end],
                'test': all_candles[val_end:]
            }
    
    def analyze_action_probabilities(self, model: nn.Module, candles: List) -> Dict:
        """Analyze what actions the model is predicting."""
        model.eval()
        
        action_analysis = {
            'buy_probs': [],
            'sell_probs': [],
            'hold_probs': [],
            'predicted_actions': [],
            'confidence_scores': [],
            'market_conditions': []
        }
        
        for i in range(self.feature_extractor.lookback_window, min(len(candles), 100)):
            try:
                features = self.feature_extractor.extract_features(candles[:i+1])
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
                
                with torch.no_grad():
                    logits, value = model(state_tensor)
                    probs = torch.softmax(logits, dim=-1)
                    
                    # Store probabilities
                    action_analysis['buy_probs'].append(probs[0, 0].item())
                    action_analysis['sell_probs'].append(probs[0, 1].item())
                    action_analysis['hold_probs'].append(probs[0, 2].item())
                    
                    # Predicted action
                    predicted_action = torch.argmax(probs, dim=-1).item()
                    action_analysis['predicted_actions'].append(predicted_action)
                    
                    # Confidence
                    confidence = torch.max(probs).item()
                    action_analysis['confidence_scores'].append(confidence)
                    
                    # Market condition
                    candle = candles[i]
                    prev_candle = candles[i-1]
                    price_change = (candle.close - prev_candle.close) / prev_candle.close
                    
                    action_analysis['market_conditions'].append({
                        'price_change': price_change,
                        'price': candle.close,
                        'volume': candle.volume
                    })
                    
            except Exception as e:
                continue
        
        return action_analysis
    
    def simulate_with_forced_trades(self, model: nn.Module, candles: List) -> Dict:
        """Simulate trading with some forced trades to see potential."""
        env = GridTradingEnv(
            initial_balance=self.initial_capital,
            risk_per_trade=0.05,
            grid_spacing=0.0025,
            take_profit_multiplier=2.0,
            fee_rate=0.001
        )
        
        env.reset(candles[0].close, candles[0].timestamp)
        
        results = {
            'natural_trades': 0,
            'forced_trades': 0,
            'natural_pnl': 0,
            'forced_pnl': 0,
            'equity_curve': [self.initial_capital],
            'actions_log': []
        }
        
        forced_count = 0
        max_forced = 5
        
        for i in range(self.feature_extractor.lookback_window, len(candles)):
            try:
                features = self.feature_extractor.extract_features(candles[:i+1])
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
                
                with torch.no_grad():
                    action_idx, _, _ = model.act(state_tensor, deterministic=True)
                    logits, _ = model(state_tensor)
                    probs = torch.softmax(logits, dim=-1)
                
                original_action = action_idx
                forced = False
                
                # Force some trades if model is too conservative
                if action_idx == 2 and forced_count < max_forced and len(env.positions) == 0:
                    candle = candles[i]
                    prev_candle = candles[i-1]
                    price_change = (candle.close - prev_candle.close) / prev_candle.close
                    
                    if abs(price_change) > 0.003:  # Significant price movement
                        action_idx = 0 if price_change > 0 else 1  # Force trade
                        forced = True
                        forced_count += 1
                
                # Convert to environment action
                action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
                action = action_map[action_idx]
                
                # Execute step
                candle = candles[i]
                prev_balance = env.balance
                
                _, _, _, _ = env.step(action, candle.close, candle.timestamp, candle)
                
                # Track results
                balance_change = env.balance - prev_balance
                
                if forced:
                    results['forced_trades'] += 1
                    results['forced_pnl'] += balance_change
                elif action != Action.HOLD:
                    results['natural_trades'] += 1
                    results['natural_pnl'] += balance_change
                
                results['equity_curve'].append(env.balance)
                results['actions_log'].append({
                    'timestamp': candle.timestamp,
                    'original_action': original_action,
                    'final_action': action_idx,
                    'forced': forced,
                    'probs': probs[0].cpu().numpy().tolist(),
                    'balance': env.balance
                })
                
            except Exception as e:
                continue
        
        results['final_balance'] = env.balance
        results['total_return'] = (env.balance / self.initial_capital - 1) * 100
        
        return results
    
    def create_simple_model(self) -> SimpleTCNPPO:
        """Create a simple model for testing."""
        feature_dim = self.feature_extractor.get_feature_dim()
        return SimpleTCNPPO(feature_dim, hidden_dim=64).to(self.device)
    
    def quick_train(self, model: nn.Module, training_data: List, episodes: int = 20):
        """Quick training with better reward shaping."""
        optimizer = torch.optim.Adam(model.parameters(), lr=3e-4)
        model.train()
        
        for episode in range(episodes):
            # Simulate episode with forced exploration
            episode_data = self.simulate_with_forced_trades(model, training_data)
            
            if len(episode_data['actions_log']) == 0:
                continue
            
            # Extract training data
            states = []
            actions = []
            rewards = []
            
            for i, action_log in enumerate(episode_data['actions_log']):
                if i < len(episode_data['equity_curve']) - 1:
                    # Calculate reward
                    equity_change = episode_data['equity_curve'][i+1] - episode_data['equity_curve'][i]
                    
                    # Reward shaping: encourage trading activity
                    if action_log['final_action'] == 2:  # HOLD
                        reward = 0.001  # Small positive for holding
                    else:
                        reward = equity_change / 15.0  # Normalize by risk amount
                        if reward > 0:
                            reward += 0.01  # Bonus for profitable trades
                    
                    # Reconstruct features
                    try:
                        idx = self.feature_extractor.lookback_window + i
                        if idx < len(training_data):
                            features = self.feature_extractor.extract_features(training_data[:idx+1])
                            states.append(features)
                            actions.append(action_log['final_action'])
                            rewards.append(reward)
                    except:
                        continue
            
            if len(states) == 0:
                continue
            
            # Training step
            try:
                states_tensor = torch.FloatTensor(np.array(states)).to(self.device)
                actions_tensor = torch.LongTensor(actions).to(self.device)
                rewards_tensor = torch.FloatTensor(rewards).to(self.device)
                
                # Normalize rewards
                if len(rewards_tensor) > 1:
                    rewards_tensor = (rewards_tensor - rewards_tensor.mean()) / (rewards_tensor.std() + 1e-8)
                
                # Forward pass
                logits, values = model(states_tensor)
                probs = torch.softmax(logits, dim=-1)
                dist = torch.distributions.Categorical(probs)
                log_probs = dist.log_prob(actions_tensor)
                entropy = dist.entropy()
                
                # Loss calculation
                policy_loss = -(log_probs * rewards_tensor).mean()
                value_loss = nn.MSELoss()(values, rewards_tensor)
                entropy_loss = -entropy.mean()
                
                total_loss = policy_loss + 0.5 * value_loss + 0.02 * entropy_loss
                
                # Backward pass
                optimizer.zero_grad()
                total_loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), 0.5)
                optimizer.step()
                
            except Exception as e:
                continue
        
        return model
    
    async def run_investigation(self) -> Dict:
        """Run quick investigation."""
        logger.info("🔍 Starting Quick Investigation")
        logger.info("=" * 50)
        
        # Collect data
        data_splits = await self.collect_test_data()
        logger.info(f"Data collected - Train: {len(data_splits['training'])}, Test: {len(data_splits['test'])}")
        
        results = {}
        
        # Test 1: Untrained model behavior
        logger.info("Testing untrained model...")
        untrained_model = self.create_simple_model()
        untrained_analysis = self.analyze_action_probabilities(untrained_model, data_splits['test'])
        untrained_simulation = self.simulate_with_forced_trades(untrained_model, data_splits['test'])
        
        results['untrained'] = {
            'analysis': untrained_analysis,
            'simulation': untrained_simulation
        }
        
        # Test 2: Trained model behavior
        logger.info("Training and testing model...")
        trained_model = self.create_simple_model()
        trained_model = self.quick_train(trained_model, data_splits['training'])
        
        trained_analysis = self.analyze_action_probabilities(trained_model, data_splits['test'])
        trained_simulation = self.simulate_with_forced_trades(trained_model, data_splits['test'])
        
        results['trained'] = {
            'analysis': trained_analysis,
            'simulation': trained_simulation
        }
        
        # Analysis summary
        logger.info("📊 INVESTIGATION RESULTS")
        logger.info("-" * 30)
        
        # Untrained model
        untrained_actions = untrained_analysis['predicted_actions']
        untrained_hold_pct = (untrained_actions.count(2) / len(untrained_actions)) * 100 if untrained_actions else 0
        
        logger.info(f"Untrained Model:")
        logger.info(f"  HOLD percentage: {untrained_hold_pct:.1f}%")
        logger.info(f"  Natural trades: {untrained_simulation['natural_trades']}")
        logger.info(f"  Final balance: ${untrained_simulation['final_balance']:.2f}")
        
        # Trained model
        trained_actions = trained_analysis['predicted_actions']
        trained_hold_pct = (trained_actions.count(2) / len(trained_actions)) * 100 if trained_actions else 0
        
        logger.info(f"Trained Model:")
        logger.info(f"  HOLD percentage: {trained_hold_pct:.1f}%")
        logger.info(f"  Natural trades: {trained_simulation['natural_trades']}")
        logger.info(f"  Forced trades: {trained_simulation['forced_trades']}")
        logger.info(f"  Final balance: ${trained_simulation['final_balance']:.2f}")
        logger.info(f"  Total return: {trained_simulation['total_return']:.2f}%")
        
        # Key insights
        logger.info("\n🔍 KEY INSIGHTS:")
        if trained_hold_pct > 90:
            logger.info("  ❌ Model is extremely conservative (>90% HOLD)")
            logger.info("  🔧 SOLUTION: Need better reward shaping and exploration")
        elif trained_hold_pct > 70:
            logger.info("  ⚠️  Model is very conservative (>70% HOLD)")
            logger.info("  🔧 SOLUTION: Increase trading incentives")
        else:
            logger.info("  ✅ Model shows reasonable trading activity")
        
        if trained_simulation['natural_trades'] == 0:
            logger.info("  ❌ No natural trades taken")
            logger.info("  🔧 SOLUTION: Reward function needs major revision")
        
        if trained_simulation['forced_trades'] > 0:
            forced_return = (trained_simulation['forced_pnl'] / 15.0) * 100  # Normalize by risk
            logger.info(f"  💡 Forced trades showed {forced_return:.2f}% potential")
        
        return results


async def main():
    """Main investigation function."""
    investigator = QuickInvestigator()
    results = await investigator.run_investigation()
    
    logger.info("\n✅ Quick investigation completed!")
    logger.info("🚀 Ready to implement fixes based on findings")


if __name__ == "__main__":
    asyncio.run(main())
