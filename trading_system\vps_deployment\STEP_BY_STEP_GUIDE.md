# 🚀 STEP-BY-STEP VPS DEPLOYMENT GUIDE

## 📋 COMPLETE DEPLOYMENT PROCESS

**VPS Details:**
- IP: ***********
- OS: Ubuntu 24.04
- Purpose: Live trading execution only

---

## 🎯 PHASE 1: PREPARE YOUR LAPTOP

### Step 1: Export Best Model
```bash
# On your laptop, in the trading_system directory
cd trading_system

# Run the deployment script to prepare everything
python vps_deployment/deploy_to_vps.py
```

### Step 2: Verify Files Created
Check that these files were created in `vps_deployment/`:
- ✅ `best_model_cycle73.pth`
- ✅ `requirements.txt`
- ✅ `.env.template`
- ✅ `supervisor_config.conf`
- ✅ `systemd_service.service`

---

## 🎯 PHASE 2: CONNECT TO VPS

### Step 1: Initial Connection
```bash
# Connect to VPS (you'll need the root password or SSH key)
ssh root@***********

# Or if you have a specific username
ssh username@***********
```

### Step 2: Create Trading User
```bash
# Create dedicated trading user for security
sudo useradd -m -s /bin/bash trading
sudo usermod -aG sudo trading
sudo passwd trading  # Set password

# Switch to trading user
sudo su - trading
```

---

## 🎯 PHASE 3: SYSTEM SETUP

### Step 1: Update System
```bash
# Update all packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git vim htop screen tmux python3.11 python3.11-pip python3.11-venv
```

### Step 2: Create Project Structure
```bash
# Create main directory
mkdir -p /home/<USER>/live_trading
cd /home/<USER>/live_trading

# Create subdirectories
mkdir -p {src/trading,src/utils,models,logs,config}
```

### Step 3: Setup Python Environment
```bash
# Create virtual environment
python3.11 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip
```

---

## 🎯 PHASE 4: TRANSFER FILES FROM LAPTOP

### Step 1: Transfer Core Files
**Run these commands from your laptop:**

```bash
# Navigate to your trading system directory
cd trading_system

# Transfer main files
scp vps_deployment/live_trading_main.py trading@***********:/home/<USER>/live_trading/
scp vps_deployment/vps_monitor.py trading@***********:/home/<USER>/live_trading/
scp vps_deployment/requirements.txt trading@***********:/home/<USER>/live_trading/
scp vps_deployment/.env.template trading@***********:/home/<USER>/live_trading/

# Transfer model
scp vps_deployment/best_model_cycle73.pth trading@***********:/home/<USER>/live_trading/models/

# Transfer source code
scp src/trading/grid_trading_env.py trading@***********:/home/<USER>/live_trading/src/trading/
scp src/trading/binance_data_fetcher.py trading@***********:/home/<USER>/live_trading/src/trading/
scp src/utils/trading_feature_extractor.py trading@***********:/home/<USER>/live_trading/src/utils/
```

### Step 2: Transfer Configuration Files
```bash
# Transfer config files
scp vps_deployment/supervisor_config.conf trading@***********:/home/<USER>/live_trading/config/
scp vps_deployment/systemd_service.service trading@***********:/home/<USER>/live_trading/config/
```

---

## 🎯 PHASE 5: VPS CONFIGURATION

### Step 1: Install Python Dependencies
**On the VPS:**
```bash
# Activate virtual environment
cd /home/<USER>/live_trading
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Configure Environment
```bash
# Copy environment template
cp .env.template .env

# Edit environment file
nano .env
```

**Add your Binance API credentials:**
```env
BINANCE_API_KEY=your_actual_api_key_here
BINANCE_SECRET_KEY=your_actual_secret_key_here
BINANCE_TESTNET=false  # Set to true for testing

# Other settings are pre-configured
```

### Step 3: Set Permissions
```bash
# Make scripts executable
chmod +x live_trading_main.py
chmod +x vps_monitor.py

# Secure environment file
chmod 600 .env

# Create log directory
mkdir -p logs
```

---

## 🎯 PHASE 6: TESTING

### Step 1: Test Model Loading
```bash
# Test if everything works
cd /home/<USER>/live_trading
source venv/bin/activate

# Run a quick test
python -c "
import torch
from live_trading_main import SimpleTCNPPO
model = SimpleTCNPPO()
checkpoint = torch.load('models/best_model_cycle73.pth', map_location='cpu')
model.load_state_dict(checkpoint)
print('✅ Model loaded successfully!')
"
```

### Step 2: Test Trading System
```bash
# Run trading system in test mode (Ctrl+C to stop)
python live_trading_main.py
```

**You should see:**
- ✅ Model loaded successfully
- ✅ Environment variables loaded
- ✅ Components initialized
- ✅ Trading loop started

---

## 🎯 PHASE 7: PRODUCTION SETUP

### Step 1: Setup Supervisor (Process Management)
```bash
# Install supervisor
sudo apt install -y supervisor

# Copy supervisor config
sudo cp config/supervisor_config.conf /etc/supervisor/conf.d/live_trading.conf

# Reload supervisor
sudo supervisorctl reread
sudo supervisorctl update

# Start trading service
sudo supervisorctl start live_trading

# Check status
sudo supervisorctl status
```

### Step 2: Setup Monitoring
```bash
# Start monitoring in background
nohup python vps_monitor.py > logs/monitor.log 2>&1 &

# Check if monitoring is running
ps aux | grep vps_monitor
```

### Step 3: Setup Firewall
```bash
# Configure firewall for security
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 443  # HTTPS only
sudo ufw deny 80   # Block HTTP

# Check firewall status
sudo ufw status
```

---

## 🎯 PHASE 8: MONITORING & MAINTENANCE

### Step 1: Check System Status
```bash
# Check trading process
sudo supervisorctl status live_trading

# Check logs
tail -f logs/trading.log

# Check system resources
htop
```

### Step 2: Monitor Trading Performance
```bash
# View recent trades
tail -n 50 logs/trading.log | grep "ORDER:"

# Check balance updates
tail -n 50 logs/trading.log | grep "balance:"

# Monitor errors
tail -n 100 logs/trading.log | grep "ERROR"
```

### Step 3: Remote Monitoring from Laptop
**From your laptop:**
```bash
# Check VPS status
ssh trading@*********** "sudo supervisorctl status"

# View recent logs
ssh trading@*********** "tail -n 20 /home/<USER>/live_trading/logs/trading.log"

# Check system resources
ssh trading@*********** "free -h && df -h"
```

---

## 🔧 TROUBLESHOOTING

### Common Issues:

**1. Model Loading Error:**
```bash
# Check model file exists
ls -la models/best_model_cycle73.pth

# Check Python path
python -c "import sys; print(sys.path)"
```

**2. API Connection Error:**
```bash
# Check environment variables
cat .env | grep BINANCE

# Test API connection
python -c "
import os
from dotenv import load_dotenv
load_dotenv()
print('API Key:', os.getenv('BINANCE_API_KEY')[:10] + '...')
"
```

**3. Process Not Starting:**
```bash
# Check supervisor logs
sudo tail -f /var/log/supervisor/supervisord.log

# Check error logs
tail -f logs/error.log
```

**4. High Resource Usage:**
```bash
# Check system resources
htop

# Check process details
ps aux | grep python
```

---

## 📊 SUCCESS INDICATORS

**✅ System is working correctly when you see:**

1. **Process Running:**
   ```bash
   sudo supervisorctl status
   # Should show: live_trading RUNNING
   ```

2. **Regular Log Entries:**
   ```bash
   tail -f logs/trading.log
   # Should show regular STATUS updates every hour
   ```

3. **Model Predictions:**
   ```bash
   grep "Model decision" logs/trading.log
   # Should show action predictions
   ```

4. **Trade Execution:**
   ```bash
   grep "ORDER:" logs/trading.log
   # Should show actual trades being executed
   ```

---

## 🎉 DEPLOYMENT COMPLETE!

**Your live trading system is now running on the VPS with:**
- ✅ TCN-CNN-PPO model loaded and running
- ✅ Real-time Binance data feed
- ✅ Automated trading execution
- ✅ Comprehensive monitoring
- ✅ Process management with auto-restart
- ✅ Security hardening

**The system will:**
- 🔄 Make trading decisions every hour
- 📊 Execute trades based on model predictions
- 📝 Log all activities for monitoring
- 🔄 Automatically restart if it crashes
- 📧 Send alerts if configured

**Development remains on your laptop while live trading runs 24/7 on the VPS!**

---

## 📱 QUICK REFERENCE COMMANDS

### Daily Monitoring Commands:
```bash
# Check trading status
ssh trading@*********** "sudo supervisorctl status live_trading"

# View recent performance
ssh trading@*********** "tail -n 10 /home/<USER>/live_trading/logs/trading.log | grep STATUS"

# Check system health
ssh trading@*********** "free -h && df -h && uptime"
```

### Emergency Commands:
```bash
# Restart trading system
ssh trading@*********** "sudo supervisorctl restart live_trading"

# Stop trading system
ssh trading@*********** "sudo supervisorctl stop live_trading"

# View error logs
ssh trading@*********** "tail -n 50 /home/<USER>/live_trading/logs/error.log"
```

### Update Model Commands:
```bash
# Transfer new model from laptop
scp new_model.pth trading@***********:/home/<USER>/live_trading/models/

# Update model path in .env and restart
ssh trading@*********** "sudo supervisorctl restart live_trading"
```
