# Grid Trading System with TCN-CNN-PPO

A high-frequency grid trading system that uses Temporal Convolutional Networks (TCN), Convolutional Neural Networks (CNN), and Proximal Policy Optimization (PPO) with Meta Reinforcement Learning for hyperparameter optimization.

## Features

- **Data Collection**: Fetches and processes OHLCV data from Binance
- **Grid Trading**: Implements a grid trading strategy with dynamic levels
- **Machine Learning**: Uses TCN-CNN for feature extraction and PPO for trading decisions
- **Meta-RL**: Optimizes hyperparameters based on accumulated net profits
- **Risk Management**: 5% risk per trade with 2:1 risk-reward ratio
- **Auto-Fix**: Built-in error recovery and self-healing mechanisms

## Prerequisites

- Python 3.9+
- TA-Lib (Technical Analysis Library)
- Binance API key (optional for live trading)

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/trading-system.git
   cd trading-system
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

## Configuration

Create a `.env` file in the project root with the following variables:

```ini
# Binance API (required for live data fetching)
BINANCE_API_KEY=your_api_key
BINANCE_API_SECRET=your_api_secret

# Trading parameters
RISK_PER_TRADE=0.05
GRID_SPACING=0.0025
DEFAULT_SYMBOL=BTCUSDT
DEFAULT_INTERVAL=1h

# Logging
LOG_LEVEL=INFO
```

## Project Structure

```
trading_system/
├── src/                    # Source code
│   ├── data/               # Data collection and processing
│   ├── models/             # ML models
│   ├── trading/            # Trading strategies
│   ├── utils/              # Utility functions
│   └── __init__.py         # Package initialization
├── tests/                  # Unit and integration tests
├── config/                 # Configuration files
├── data/                   # Data storage
│   └── cache/             # Cached market data
├── notebooks/              # Jupyter notebooks for analysis
├── .env.example           # Example environment variables
├── pyproject.toml         # Project metadata and dependencies
└── README.md              # This file
```

## Usage

### Data Collection

```python
from src.data.binance_fetcher import BinanceDataFetcher
import asyncio

async def main():
    async with BinanceDataFetcher() as fetcher:
        # Fetch historical data
        response = await fetcher.fetch_historical_data(
            symbol="BTCUSDT",
            interval="1h",
            limit=1000
        )
        
        if response.success:
            print(f"Fetched {len(response.data)} candles")
            print(f"First candle: {response.data[0]}")
        else:
            print(f"Error: {response.error}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=src tests/

# Run a specific test
pytest tests/test_data_fetcher.py -v
```

## Development

### Code Style

This project uses:
- Black for code formatting
- isort for import sorting
- mypy for static type checking

Run the following before committing:

```bash
black .
isort .
mypy .
```

### Versioning

We use [SemVer](https://semver.org/) for versioning. For the versions available, see the [tags on this repository](https://github.com/yourusername/trading-system/tags).

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Binance for the market data API
- The developers of the open-source libraries used in this project
