["trading_system/tests/test_data_fetcher.py::test_data_validation", "trading_system/tests/test_data_fetcher.py::test_fetch_historical_data", "trading_system/tests/test_data_fetcher.py::test_fetch_klines_success", "trading_system/tests/test_data_fetcher.py::test_fetch_real_historical_data", "trading_system/tests/test_data_fetcher.py::test_fetch_real_klines_success", "trading_system/tests/test_data_fetcher.py::test_real_data_validation", "trading_system/tests/test_grid_trading_env.py::test_grid_initialization", "trading_system/tests/test_grid_trading_env.py::test_grid_level_management", "trading_system/tests/test_grid_trading_env.py::test_initialization", "trading_system/tests/test_grid_trading_env.py::test_long_position", "trading_system/tests/test_grid_trading_env.py::test_metrics_calculation", "trading_system/tests/test_grid_trading_env.py::test_position_pnl", "trading_system/tests/test_grid_trading_env.py::test_position_stop_loss", "trading_system/tests/test_grid_trading_env.py::test_position_take_profit", "trading_system/tests/test_grid_trading_env.py::test_position_ttl", "trading_system/tests/test_grid_trading_env.py::test_reset", "trading_system/tests/test_grid_trading_env.py::test_short_position"]