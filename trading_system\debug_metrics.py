"""
Debug script to understand the metrics calculation issue.
"""
import asyncio
import sys
from datetime import datetime, timedelta, timezone

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action


async def debug_metrics():
    """Debug the metrics calculation."""
    print("🔍 Debugging Metrics Calculation")
    print("=" * 40)
    
    # Get some real data
    async with BinanceDataFetcher() as fetcher:
        response = await fetcher.fetch_klines("BTCUSDT", "1h", limit=3)
        candles = response.data
    
    # Initialize environment
    env = GridTradingEnv(initial_balance=10000.0, risk_per_trade=0.05)
    
    # Reset with first candle
    first_candle = candles[0]
    print(f"Initial price: ${first_candle.close:.2f}")
    print(f"Initial balance: ${env.balance:.2f}")
    print(f"Initial equity: ${env.equity:.2f}")
    
    state = env.reset(first_candle.close, first_candle.timestamp)
    print(f"After reset - Balance: ${env.balance:.2f}, Equity: ${env.equity:.2f}")
    
    # Execute a sell action
    second_candle = candles[1]
    print(f"\nExecuting SELL at ${second_candle.close:.2f}")
    
    state, reward, done, info = env.step(Action.SELL, second_candle.close, second_candle.timestamp)
    
    print(f"After SELL - Balance: ${env.balance:.2f}, Equity: ${env.equity:.2f}")
    print(f"Open positions: {len(env.positions)}")
    if env.positions:
        pos = env.positions[0]
        print(f"Position: {pos.position_type}, size: {pos.size:.4f}, entry: ${pos.entry_price:.2f}")
        print(f"Position P&L: ${pos.pnl:.2f} ({pos.pnl_pct:.2%})")
    
    # Move to next candle to trigger stop loss
    third_candle = candles[2]
    print(f"\nMoving to ${third_candle.close:.2f}")
    
    state, reward, done, info = env.step(Action.HOLD, third_candle.close, third_candle.timestamp)
    
    print(f"After move - Balance: ${env.balance:.2f}, Equity: ${env.equity:.2f}")
    print(f"Open positions: {len(env.positions)}")
    print(f"Closed positions: {len(env.closed_positions)}")
    
    if env.closed_positions:
        pos = env.closed_positions[-1]
        print(f"Closed position P&L: ${pos.pnl:.2f} ({pos.pnl_pct:.2%})")
    
    # Get metrics
    metrics = env.get_metrics()
    print(f"\nFinal Metrics:")
    print(f"Balance: ${metrics['balance']:.2f}")
    print(f"Equity: ${metrics['equity']:.2f}")
    print(f"Return: {metrics['return_pct']:.2f}%")
    print(f"Expected return: {(metrics['balance'] / 10000 - 1) * 100:.2f}%")
    
    # Check equity curve
    print(f"\nEquity curve:")
    for timestamp, equity in env.equity_curve:
        print(f"  {timestamp}: ${equity:.2f}")


if __name__ == "__main__":
    asyncio.run(debug_metrics())
