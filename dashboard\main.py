"""Web dashboard for monitoring the trading system."""

import os
import json
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any

import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse, FileResponse
import pandas as pd
import numpy as np
from pydantic import BaseModel

# Setup paths
BASE_DIR = Path(__file__).parent
STATIC_DIR = BASE_DIR / "static"
TEMPLATES_DIR = BASE_DIR / "templates"
DATA_DIR = Path("live_results")  # Directory where live trading data is saved

# Create necessary directories
for directory in [STATIC_DIR, TEMPLATES_DIR, DATA_DIR]:
    directory.mkdir(exist_ok=True, parents=True)

# Initialize FastAPI app
app = FastAPI(title="Trading System Dashboard")

# Mount static files
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# Setup templates
templates = Jinja2Templates(directory=TEMPLATES_DIR)

# WebSocket manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)

    async def broadcast(self, message: str):
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                print(f"Error broadcasting message: {e}")

manager = ConnectionManager()

# Data models
class Trade(BaseModel):
    timestamp: str
    action: str
    price: float
    size: float
    cost: Optional[float] = None
    proceeds: Optional[float] = None
    balance: float
    position: float

class PerformanceMetrics(BaseModel):
    timestamp: str
    equity: float
    daily_return: Optional[float] = None
    sharpe_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None
    win_rate: Optional[float] = None
    num_trades: int = 0

# Routes
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    """Render the main dashboard page."""
    return templates.TemplateResponse(
        "index.html",
        {"request": request, "title": "Trading System Dashboard"}
    )

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """Handle WebSocket connections for real-time updates."""
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        manager.disconnect(websocket)

# API Endpoints
@app.get("/api/equity")
async def get_equity() -> List[Dict[str, Any]]:
    """Get equity curve data."""
    try:
        equity_file = DATA_DIR / "equity.csv"
        if not equity_file.exists():
            return []
            
        df = pd.read_csv(equity_file)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # Resample to daily if needed
        if len(df) > 1000:
            df = df.set_index('timestamp').resample('1H').last().reset_index()
        
        return df.to_dict('records')
    except Exception as e:
        print(f"Error loading equity data: {e}")
        return []

@app.get("/api/trades")
async def get_trades(limit: int = 100) -> List[Dict[str, Any]]:
    """Get recent trades."""
    try:
        trades_file = DATA_DIR / "trades.csv"
        if not trades_file.exists():
            return []
            
        df = pd.read_csv(trades_file, parse_dates=['timestamp'])
        df = df.sort_values('timestamp', ascending=False).head(limit)
        return df.to_dict('records')
    except Exception as e:
        print(f"Error loading trades: {e}")
        return []

@app.get("/api/performance")
async def get_performance() -> Dict[str, Any]:
    """Get performance metrics."""
    try:
        metrics_file = DATA_DIR / "summary.json"
        if not metrics_file.exists():
            return {}
            
        with open(metrics_file, 'r') as f:
            metrics = json.load(f)
            
        # Load equity data for additional calculations
        equity_data = await get_equity()
        if equity_data:
            df = pd.DataFrame(equity_data)
            returns = df['equity'].pct_change().dropna()
            
            # Calculate additional metrics
            metrics['sharpe_ratio'] = (returns.mean() / (returns.std() + 1e-9)) * np.sqrt(252)
            metrics['max_drawdown'] = (1 - df['equity'] / df['equity'].cummax()).max() * 100
            
            # Calculate daily returns
            df['date'] = pd.to_datetime(df['timestamp']).dt.date
            daily_returns = df.groupby('date')['equity'].last().pct_change().dropna()
            metrics['daily_volatility'] = daily_returns.std() * 100
            metrics['avg_daily_return'] = daily_returns.mean() * 100
            
        return metrics
    except Exception as e:
        print(f"Error calculating performance metrics: {e}")
        return {}

@app.get("/api/market-data")
async def get_market_data() -> Dict[str, Any]:
    """Get current market data."""
    # This would typically fetch from your data source
    # For now, return mock data
    return {
        "btc_price": 50000 + (np.random.random() * 1000 - 500),
        "volume_24h": 2500000000 + (np.random.random() * 1000000000 - 500000000),
        "market_cap": 950000000000 + (np.random.random() * 10000000000 - 5000000000),
        "price_change_24h": (np.random.random() * 10 - 5),
        "fear_greed": int(np.random.random() * 100)
    }

# Background task to broadcast updates
async def broadcast_updates():
    """Periodically broadcast updates to all connected clients."""
    while True:
        try:
            # Get latest data
            equity_data = await get_equity()
            performance_data = await get_performance()
            market_data = await get_market_data()
            recent_trades = await get_trades(limit=5)
            
            # Prepare update
            update = {
                "type": "update",
                "timestamp": datetime.utcnow().isoformat(),
                "equity": equity_data[-1]["equity"] if equity_data else 0,
                "performance": performance_data,
                "market": market_data,
                "recent_trades": recent_trades
            }
            
            # Broadcast to all connected clients
            await manager.broadcast(json.dumps(update))
            
        except Exception as e:
            print(f"Error in broadcast task: {e}")
        
        await asyncio.sleep(5)  # Update every 5 seconds

# Startup event
@app.on_event("startup")
async def startup_event():
    """Start background tasks on application startup."""
    asyncio.create_task(broadcast_updates())

# Error handlers
@app.exception_handler(404)
async def not_found_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=404,
        content={"message": "The requested resource was not found"},
    )

@app.exception_handler(500)
async def server_error_exception_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content={"message": "An internal server error occurred"},
    )

# Static file serving for SPA
@app.get("/{full_path:path}")
async def serve_spa(full_path: str):
    """Serve the single-page application."""
    # Try to serve the requested file
    static_file = STATIC_DIR / full_path
    if static_file.exists() and static_file.is_file():
        return FileResponse(static_file)
    
    # Default to index.html for SPA routing
    return FileResponse(TEMPLATES_DIR / "index.html")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
