#!/usr/bin/env python3
"""
Retrain TCN-CNN-PPO Model for Live VPS Deployment
Early stopping at 0.85 composite score with automatic model saving
"""
import asyncio
import sys
import json
import torch
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from comprehensive_1000_cycle_evaluation import Comprehensive1000CycleEvaluator


class LiveDeploymentTrainer:
    """Train model specifically for live VPS deployment with 0.85 early stopping."""

    def __init__(self, initial_capital: float = 300.0):
        self.initial_capital = initial_capital
        self.evaluator = Comprehensive1000CycleEvaluator(initial_capital=initial_capital)
        self.early_stop_threshold = 0.85  # Stop at 0.85 composite score
        self.max_cycles = 10000  # Maximum cycles to prevent infinite training
        
        # Best model tracking
        self.best_composite_score = 0.0
        self.best_model_state = None
        self.best_cycle_data = None
        self.early_stop_triggered = False
        
        # Setup logging
        self.setup_logging()
        
        self.logger.info("Live Deployment Trainer initialized")
        self.logger.info(f"Early stop threshold: {self.early_stop_threshold}")
        self.logger.info(f"Maximum cycles: {self.max_cycles}")

    def setup_logging(self):
        """Setup comprehensive logging."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'live_deployment_training.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('LiveDeploymentTrainer')

    async def train_for_live_deployment(self) -> Dict:
        """Train model until 0.85 composite score is achieved."""
        print("🚀 TRAINING FOR LIVE VPS DEPLOYMENT")
        print("=" * 60)
        print(f"🎯 Target: ≥{self.early_stop_threshold} composite score")
        print(f"💰 Initial Capital: ${self.initial_capital}")
        print(f"🔄 Max Cycles: {self.max_cycles}")
        print("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # Collect evaluation data
            print("\n📊 Collecting training data...")
            data_splits = await self.evaluator.collect_evaluation_data()
            print(f"✅ Data collected: {len(data_splits['training'])} training samples")
            
            # Training loop with early stopping
            for cycle in range(self.max_cycles):
                cycle_start = datetime.now()
                
                print(f"\n🔄 CYCLE {cycle + 1}/{self.max_cycles}")
                print(f"⏰ Started: {cycle_start.strftime('%H:%M:%S')}")
                
                # Run single cycle
                cycle_result = await self.evaluator.run_single_cycle(cycle, data_splits)
                
                # Extract performance metrics
                testing_data = cycle_result.get('testing', {})
                composite_score = testing_data.get('composite_score', 0)
                total_return = testing_data.get('total_return', 0)
                final_balance = testing_data.get('final_balance', self.initial_capital)
                max_drawdown = testing_data.get('max_drawdown', 0)
                
                # Update best model if this is better
                if composite_score > self.best_composite_score:
                    self.best_composite_score = composite_score
                    self.best_model_state = cycle_result.get('model_state_dict')
                    self.best_cycle_data = {
                        'cycle': cycle,
                        'composite_score': composite_score,
                        'total_return': total_return,
                        'final_balance': final_balance,
                        'max_drawdown': max_drawdown,
                        'testing_data': testing_data,
                        'detailed_trades': cycle_result.get('detailed_trades', [])
                    }
                    
                    print(f"🏆 NEW BEST MODEL!")
                    print(f"   Composite Score: {composite_score:.4f}")
                    print(f"   Total Return: {total_return:+,.2f}%")
                    print(f"   Final Balance: ${final_balance:,.2f}")
                    print(f"   Max Drawdown: {max_drawdown:.2f}%")
                
                # Check early stopping condition
                if composite_score >= self.early_stop_threshold:
                    print(f"\n🎯 EARLY STOPPING TRIGGERED!")
                    print(f"🏆 Achieved composite score: {composite_score:.4f} (≥ {self.early_stop_threshold})")
                    print(f"🚀 Saving model for live deployment...")
                    
                    self.early_stop_triggered = True
                    
                    # Save the model immediately
                    model_file = await self.save_live_deployment_model(cycle_result, cycle)
                    
                    # Calculate training time
                    total_time = datetime.now() - start_time
                    
                    print(f"\n🎉 TRAINING COMPLETE!")
                    print(f"📊 Cycles completed: {cycle + 1}")
                    print(f"⏰ Total time: {total_time}")
                    print(f"📄 Model saved: {model_file}")
                    
                    return {
                        'success': True,
                        'cycles_completed': cycle + 1,
                        'total_time': str(total_time),
                        'best_composite_score': composite_score,
                        'model_file': model_file,
                        'final_performance': testing_data
                    }
                
                # Progress reporting
                cycle_time = datetime.now() - cycle_start
                elapsed = datetime.now() - start_time
                avg_time_per_cycle = elapsed.total_seconds() / (cycle + 1)
                
                print(f"📊 Cycle {cycle + 1} Results:")
                print(f"   Composite Score: {composite_score:.4f} (Best: {self.best_composite_score:.4f})")
                print(f"   Total Return: {total_return:+,.2f}%")
                print(f"   Final Balance: ${final_balance:,.2f}")
                print(f"   Max Drawdown: {max_drawdown:.2f}%")
                print(f"   Cycle Time: {cycle_time}")
                print(f"   Progress: {(composite_score/self.early_stop_threshold)*100:.1f}% to target")
                
                # Log to file
                self.logger.info(f"Cycle {cycle + 1}: Score={composite_score:.4f}, Return={total_return:+.2f}%, Balance=${final_balance:.2f}")
            
            # If we reach here, we didn't achieve the target
            print(f"\n⚠️ MAXIMUM CYCLES REACHED")
            print(f"📊 Best composite score achieved: {self.best_composite_score:.4f}")
            print(f"🎯 Target was: {self.early_stop_threshold}")
            
            if self.best_model_state is not None:
                print(f"🚀 Saving best model anyway...")
                model_file = await self.save_best_model_fallback()
                
                return {
                    'success': False,
                    'reason': 'max_cycles_reached',
                    'cycles_completed': self.max_cycles,
                    'best_composite_score': self.best_composite_score,
                    'model_file': model_file,
                    'target_score': self.early_stop_threshold
                }
            else:
                return {
                    'success': False,
                    'reason': 'no_valid_model',
                    'cycles_completed': self.max_cycles
                }
                
        except Exception as e:
            self.logger.error(f"Training failed: {e}")
            print(f"❌ Training failed: {e}")
            raise

    async def save_live_deployment_model(self, cycle_result: Dict, cycle_number: int) -> str:
        """Save model for live VPS deployment."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create directories
        models_dir = Path("models")
        models_dir.mkdir(exist_ok=True)
        
        live_models_dir = Path("live_deployment_models")
        live_models_dir.mkdir(exist_ok=True)
        
        # Extract performance data
        testing_data = cycle_result.get('testing', {})
        composite_score = testing_data.get('composite_score', 0)
        
        # Save PyTorch model file
        model_filename = f"live_model_cycle{cycle_number}_score{composite_score:.4f}_{timestamp}.pth"
        model_path = live_models_dir / model_filename
        
        # Save model state dict
        model_state = cycle_result.get('model_state_dict')
        if model_state is None:
            raise ValueError("Model state dict not found in cycle result")
        
        torch.save(model_state, model_path)
        
        # Create comprehensive deployment package
        deployment_package = {
            'model_info': {
                'filename': model_filename,
                'cycle_number': cycle_number,
                'composite_score': composite_score,
                'total_return': testing_data.get('total_return', 0),
                'final_balance': testing_data.get('final_balance', self.initial_capital),
                'max_drawdown': testing_data.get('max_drawdown', 0),
                'created_timestamp': timestamp,
                'model_architecture': 'SimpleTCNPPO',
                'input_size': 216,
                'hidden_size': 128,
                'num_actions': 3,
                'early_stop_threshold': self.early_stop_threshold,
                'achieved_target': True
            },
            'trading_config': {
                'initial_capital': self.initial_capital,
                'risk_per_trade': 0.05,  # 5% risk model
                'grid_spacing': 0.0025,  # 0.25% grid spacing
                'take_profit_multiplier': 2.0,  # 2:1 risk-reward ratio
                'fee_rate': 0.001,  # 0.1% commission
                'symbol': 'BTCUSDT',
                'timeframe': '1h',
                'max_positions': 5,
                'stop_loss_pct': 0.05
            },
            'performance_metrics': testing_data,
            'detailed_trades': cycle_result.get('detailed_trades', [])[:10],  # First 10 trades
            'vps_deployment': {
                'ready_for_live_trading': True,
                'model_path': str(model_path),
                'deployment_date': timestamp,
                'target_achieved': True,
                'recommended_for_live': True
            }
        }
        
        # Save deployment package
        package_file = live_models_dir / f"deployment_package_{timestamp}.json"
        with open(package_file, 'w') as f:
            json.dump(deployment_package, f, indent=2, default=str)
        
        # Also save a copy in models directory for backup
        backup_path = models_dir / model_filename
        torch.save(model_state, backup_path)
        
        print(f"✅ Model saved: {model_path}")
        print(f"✅ Deployment package: {package_file}")
        print(f"✅ Backup saved: {backup_path}")
        
        self.logger.info(f"Live deployment model saved: {model_path}")
        
        return str(model_path)

    async def save_best_model_fallback(self) -> str:
        """Save best model if target wasn't reached."""
        if self.best_model_state is None or self.best_cycle_data is None:
            raise ValueError("No best model available to save")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create directories
        models_dir = Path("models")
        models_dir.mkdir(exist_ok=True)
        
        live_models_dir = Path("live_deployment_models")
        live_models_dir.mkdir(exist_ok=True)
        
        # Save model
        model_filename = f"best_model_score{self.best_composite_score:.4f}_{timestamp}.pth"
        model_path = live_models_dir / model_filename
        
        torch.save(self.best_model_state, model_path)
        
        # Create deployment package
        deployment_package = {
            'model_info': {
                'filename': model_filename,
                'cycle_number': self.best_cycle_data['cycle'],
                'composite_score': self.best_composite_score,
                'total_return': self.best_cycle_data['total_return'],
                'final_balance': self.best_cycle_data['final_balance'],
                'max_drawdown': self.best_cycle_data['max_drawdown'],
                'created_timestamp': timestamp,
                'model_architecture': 'SimpleTCNPPO',
                'early_stop_threshold': self.early_stop_threshold,
                'achieved_target': False,
                'note': 'Best model from training - target not reached'
            },
            'trading_config': {
                'initial_capital': self.initial_capital,
                'risk_per_trade': 0.05,
                'grid_spacing': 0.0025,
                'take_profit_multiplier': 2.0,
                'fee_rate': 0.001,
                'symbol': 'BTCUSDT',
                'timeframe': '1h'
            },
            'performance_metrics': self.best_cycle_data['testing_data'],
            'vps_deployment': {
                'ready_for_live_trading': self.best_composite_score >= 0.80,  # Still good if ≥ 0.80
                'model_path': str(model_path),
                'deployment_date': timestamp,
                'target_achieved': False,
                'recommended_for_live': self.best_composite_score >= 0.80
            }
        }
        
        # Save package
        package_file = live_models_dir / f"best_model_package_{timestamp}.json"
        with open(package_file, 'w') as f:
            json.dump(deployment_package, f, indent=2, default=str)
        
        print(f"✅ Best model saved: {model_path}")
        print(f"✅ Package saved: {package_file}")
        
        return str(model_path)


async def main():
    """Main training function."""
    print("🚀 LIVE DEPLOYMENT MODEL TRAINING")
    print("=" * 50)
    
    # Initialize trainer
    trainer = LiveDeploymentTrainer(initial_capital=300.0)
    
    try:
        # Start training
        result = await trainer.train_for_live_deployment()
        
        if result['success']:
            print(f"\n🎉 TRAINING SUCCESSFUL!")
            print(f"🏆 Achieved composite score: {result['best_composite_score']:.4f}")
            print(f"📊 Cycles completed: {result['cycles_completed']}")
            print(f"⏰ Training time: {result['total_time']}")
            print(f"📄 Model ready for VPS: {result['model_file']}")
        else:
            print(f"\n⚠️ Training completed but target not reached")
            print(f"📊 Best score: {result.get('best_composite_score', 'N/A')}")
            print(f"🎯 Target was: {result.get('target_score', 0.85)}")
            if 'model_file' in result:
                print(f"📄 Best model saved: {result['model_file']}")
        
    except Exception as e:
        print(f"\n❌ Training failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())
