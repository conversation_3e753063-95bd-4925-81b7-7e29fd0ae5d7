# Binance API (required for live data fetching)
BINANCE_API_KEY=your_api_key_here
BINANCE_API_SECRET=your_api_secret_here

# Application settings
DEBUG=True
LOG_LEVEL=INFO

# Data settings
DATA_DIR=./data
CACHE_DIR=./data/cache

# Trading parameters
DEFAULT_SYMBOL=BTCUSDT
DEFAULT_INTERVAL=1h
RISK_PER_TRADE=0.05
GRID_SPACING=0.0025
TAKE_PROFIT_MULTIPLIER=2.0

# Database
DATABASE_URL=sqlite:///./trading_system.db
