"""
Integrated ML Training Pipeline with Validated Trading Logic
Combines TCN-CNN-PPO with real market data and correct risk management
"""
import asyncio
import sys
import numpy as np
import torch
import torch.nn as nn
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add parent directories to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.config.settings import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TradingFeatureExtractor:
    """Extract features from OHLCV data for ML training."""

    def __init__(self, lookback_window: int = 24):
        self.lookback_window = lookback_window

    def extract_features(self, candles: List) -> np.ndarray:
        """Extract features from OHLCV candles."""
        if len(candles) < self.lookback_window:
            raise ValueError(f"Need at least {self.lookback_window} candles")

        # Use last lookback_window candles
        recent_candles = candles[-self.lookback_window:]

        features = []
        for candle in recent_candles:
            # Basic OHLCV features
            ohlcv = [
                candle.open,
                candle.high,
                candle.low,
                candle.close,
                candle.volume
            ]

            # Price ratios
            price_ratios = [
                candle.high / candle.open,
                candle.low / candle.open,
                candle.close / candle.open,
                (candle.high - candle.low) / candle.open,  # Range
            ]

            features.extend(ohlcv + price_ratios)

        return np.array(features, dtype=np.float32)

    def get_feature_dim(self) -> int:
        """Get the dimension of extracted features."""
        return self.lookback_window * 9  # 5 OHLCV + 4 ratios per candle


class SimpleTCNPPO(nn.Module):
    """Simplified TCN-PPO model for trading decisions."""

    def __init__(self, feature_dim: int, hidden_dim: int = 128):
        super().__init__()
        self.feature_dim = feature_dim

        # Feature processing
        self.feature_net = nn.Sequential(
            nn.Linear(feature_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2)
        )

        # Policy head (3 actions: BUY, SELL, HOLD)
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 3)
        )

        # Value head
        self.value_head = nn.Sequential(
            nn.Linear(hidden_dim, 64),
            nn.ReLU(),
            nn.Linear(64, 1)
        )

        # Initialize weights
        self.apply(self._init_weights)

    def _init_weights(self, module):
        if isinstance(module, nn.Linear):
            nn.init.orthogonal_(module.weight, gain=0.01)
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        features = self.feature_net(x)
        logits = self.policy_head(features)
        value = self.value_head(features).squeeze(-1)
        return logits, value

    def act(self, x: torch.Tensor, deterministic: bool = False) -> Tuple[int, float, float]:
        """Select action given state."""
        logits, value = self.forward(x)
        probs = torch.softmax(logits, dim=-1)

        if deterministic:
            action = torch.argmax(probs, dim=-1)
        else:
            dist = torch.distributions.Categorical(probs)
            action = dist.sample()

        log_prob = torch.log(probs[0, action.item()] + 1e-10)
        return action.item(), log_prob.item(), value.item()


class IntegratedTrainingPipeline:
    """Complete training pipeline integrating ML with validated trading logic."""

    def __init__(self, initial_balance: float = 300.0):
        self.initial_balance = initial_balance
        self.feature_extractor = TradingFeatureExtractor(lookback_window=24)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

        # Initialize model
        feature_dim = self.feature_extractor.get_feature_dim()
        self.model = SimpleTCNPPO(feature_dim).to(self.device)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=3e-4)

        logger.info(f"Initialized model with {feature_dim} features on {self.device}")

    async def collect_training_data(self, days: int = 30) -> List:
        """Collect real market data for training."""
        logger.info(f"Collecting {days} days of real market data...")

        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=days)

            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )

            if not response.success:
                raise RuntimeError(f"Failed to fetch data: {response.error}")

            logger.info(f"Collected {len(response.data)} candles")
            return response.data

    def simulate_episode(self, candles: List, model: SimpleTCNPPO = None) -> Dict:
        """Simulate a trading episode with the model."""
        env = GridTradingEnv(
            initial_balance=self.initial_balance,
            risk_per_trade=0.05,
            grid_spacing=0.0025,
            take_profit_multiplier=2.0,
            fee_rate=0.001
        )

        # Reset environment
        env.reset(candles[0].close, candles[0].timestamp)

        episode_data = {
            'states': [],
            'actions': [],
            'rewards': [],
            'log_probs': [],
            'values': [],
            'dones': []
        }

        total_reward = 0

        for i in range(self.feature_extractor.lookback_window, len(candles)):
            # Extract features
            try:
                features = self.feature_extractor.extract_features(candles[:i+1])
                state_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)

                # Get action from model or random
                if model is not None:
                    action_idx, log_prob, value = model.act(state_tensor)
                else:
                    # Random baseline
                    action_idx = np.random.choice(3)
                    log_prob = np.log(1/3)
                    value = 0.0

                # Convert to environment action
                action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
                action = action_map[action_idx]

                # Execute step
                candle = candles[i]
                prev_balance = env.balance
                state, reward, done, info = env.step(action, candle.close, candle.timestamp, candle)

                # Calculate reward based on balance change and risk management
                balance_change = env.balance - prev_balance
                reward = balance_change / self.initial_balance  # Normalize by initial balance

                # Store episode data
                episode_data['states'].append(features)
                episode_data['actions'].append(action_idx)
                episode_data['rewards'].append(reward)
                episode_data['log_probs'].append(log_prob)
                episode_data['values'].append(value)
                episode_data['dones'].append(done)

                total_reward += reward

            except ValueError:
                continue

        # Final metrics
        metrics = env.get_metrics()
        episode_data['final_metrics'] = metrics
        episode_data['total_reward'] = total_reward

        return episode_data

    async def train_model(self, episodes: int = 100, save_path: str = "models/tcn_ppo_trading.pth"):
        """Train the TCN-PPO model on real market data."""
        logger.info(f"Starting training for {episodes} episodes...")

        # Collect training data
        training_data = await self.collect_training_data(days=90)  # 90 days of data

        best_reward = float('-inf')
        training_history = []

        for episode in range(episodes):
            # Simulate episode
            episode_data = self.simulate_episode(training_data, self.model)

            # Calculate returns and advantages (simplified)
            rewards = episode_data['rewards']
            values = episode_data['values']

            returns = []
            running_return = 0
            for reward in reversed(rewards):
                running_return = reward + 0.99 * running_return
                returns.insert(0, running_return)

            returns = torch.FloatTensor(returns).to(self.device)
            values = torch.FloatTensor(values).to(self.device)
            advantages = returns - values

            # Normalize advantages
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)

            # Update model
            states = torch.FloatTensor(np.array(episode_data['states'])).to(self.device)
            actions = torch.LongTensor(episode_data['actions']).to(self.device)
            old_log_probs = torch.FloatTensor(episode_data['log_probs']).to(self.device)

            # Forward pass
            logits, new_values = self.model(states)
            probs = torch.softmax(logits, dim=-1)
            dist = torch.distributions.Categorical(probs)
            new_log_probs = dist.log_prob(actions)
            entropy = dist.entropy()

            # PPO loss
            ratio = (new_log_probs - old_log_probs).exp()
            surr1 = ratio * advantages
            surr2 = torch.clamp(ratio, 0.8, 1.2) * advantages
            policy_loss = -torch.min(surr1, surr2).mean()

            value_loss = nn.MSELoss()(new_values, returns)
            entropy_loss = -entropy.mean()

            total_loss = policy_loss + 0.5 * value_loss + 0.01 * entropy_loss

            # Backward pass
            self.optimizer.zero_grad()
            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 0.5)
            self.optimizer.step()

            # Logging
            total_reward = episode_data['total_reward']
            final_balance = episode_data['final_metrics']['balance']

            training_history.append({
                'episode': episode,
                'total_reward': total_reward,
                'final_balance': final_balance,
                'policy_loss': policy_loss.item(),
                'value_loss': value_loss.item(),
                'entropy_loss': entropy_loss.item()
            })

            if episode % 10 == 0:
                logger.info(f"Episode {episode}: Reward={total_reward:.4f}, "
                           f"Balance=${final_balance:.2f}, Loss={total_loss.item():.4f}")

            # Save best model
            if total_reward > best_reward:
                best_reward = total_reward
                Path(save_path).parent.mkdir(parents=True, exist_ok=True)
                torch.save({
                    'model_state_dict': self.model.state_dict(),
                    'optimizer_state_dict': self.optimizer.state_dict(),
                    'episode': episode,
                    'best_reward': best_reward,
                    'training_history': training_history
                }, save_path)
                logger.info(f"Saved new best model with reward {best_reward:.4f}")

        logger.info("Training completed!")
        return training_history


async def main():
    """Main training function."""
    logger.info("🚀 Starting Integrated ML Training Pipeline")
    logger.info("=" * 60)

    # Initialize training pipeline
    pipeline = IntegratedTrainingPipeline(initial_balance=10000.0)

    # Train model
    history = await pipeline.train_model(episodes=50)

    logger.info("✅ Training completed successfully!")
    logger.info(f"📊 Trained for {len(history)} episodes")
    logger.info(f"🎯 Best reward: {max(h['total_reward'] for h in history):.4f}")


if __name__ == "__main__":
    asyncio.run(main())
