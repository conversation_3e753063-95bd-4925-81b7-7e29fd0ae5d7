#!/bin/bash

# Update system
sudo apt update
sudo apt upgrade -y

# Install required packages
sudo apt install -y python3-pip python3-venv git

# Create project directory
mkdir -p ~/trading-system
cd ~/trading-system

# Create and activate virtual environment
python3 -m venv venv
source venv/bin/activate

# Clone repository or copy files
# git clone <your-repo-url> .
# OR copy files manually

# Install Python dependencies
pip install -r requirements.txt

# Install systemd service
sudo cp grid-trading.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable grid-trading.service

# Create .env file with your configuration
echo "Please configure your .env file with the following variables:"
echo "BINANCE_API_KEY=your_api_key"
echo "BINANCE_API_SECRET=your_api_secret"
echo "EMAIL_ENABLED=true"
echo "SMTP_SERVER=smtp.gmail.com"
echo "SMTP_PORT=587"
echo "EMAIL_USERNAME=<EMAIL>"
echo "EMAIL_PASSWORD=your_app_password"
echo "EMAIL_RECIPIENT=<EMAIL>"

# Set proper permissions
chmod 600 .env

# Start the service
echo "Starting grid trading service..."
sudo systemctl start grid-trading.service

# Check status
sudo systemctl status grid-trading.service

echo "\nSetup complete! The trading system is now running."
echo "To view logs: journalctl -u grid-trading.service -f"
