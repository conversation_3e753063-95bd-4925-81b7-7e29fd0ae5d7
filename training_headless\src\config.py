import os
import yaml
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

@dataclass
class DataConfig:
    """Configuration for data loading and preprocessing."""
    data_dir: str = "data"
    train_ratio: float = 0.8
    val_ratio: float = 0.1
    test_ratio: float = 0.1
    seq_len: int = 16
    batch_size: int = 128
    num_workers: int = 4
    pin_memory: bool = True
    
    # Data augmentation
    use_augmentation: bool = True
    aug_prob: float = 0.3
    
    # Feature scaling
    scale_features: bool = True
    scale_method: str = "minmax"  # "minmax" or "standard"

    
    # Technical indicators
    indicators: List[str] = field(default_factory=lambda: ["sma_10", "sma_50", "rsi_14", "bb_20_2"])
    
    # Data sources
    symbols: List[str] = field(default_factory=lambda: ["BTC/USDT"])
    timeframes: List[str] = field(default_factory=lambda: ["1h"])
    
    def __post_init__(self):
        # Validate ratios
        total = self.train_ratio + self.val_ratio + self.test_ratio
        if not 0.99 <= total <= 1.01:  # Allow for floating point imprecision
            raise ValueError(f"Data split ratios must sum to 1, got {total}")

@dataclass
class ModelConfig:
    """Configuration for the TCN-CNN-PPO model."""
    # Architecture
    obs_dim: int = 32  # Will be set based on data
    action_dim: int = 3  # Buy, Sell, Hold
    seq_len: int = 16
    
    # TCN parameters
    tcn_channels: List[int] = field(default_factory=lambda: [64, 64, 64])
    tcn_kernel_size: int = 3
    tcn_dropout: float = 0.2
    
    # CNN parameters
    cnn_filters: List[int] = field(default_factory=lambda: [32, 64, 128])
    cnn_kernel_sizes: List[int] = field(default_factory=lambda: [3, 3, 3])
    cnn_strides: List[int] = field(default_factory=lambda: [1, 1, 1])
    cnn_dropout: float = 0.2
    
    # Output heads
    policy_hidden_dims: List[int] = field(default_factory=lambda: [128, 64])
    value_hidden_dims: List[int] = field(default_factory=lambda: [128, 64])
    
    # Initialization
    init_type: str = "xavier"  # "xavier", "kaiming", or "orthogonal"
    init_gain: float = 0.02

@dataclass
class TrainingConfig:
    """Configuration for training the agent."""
    # Training parameters
    num_episodes: int = 1000
    max_steps_per_episode: int = 1000
    batch_size: int = 128
    gamma: float = 0.99
    gae_lambda: float = 0.95
    clip_eps: float = 0.2
    entropy_coef: float = 0.01
    value_coef: float = 0.5
    max_grad_norm: float = 0.5
    
    # Optimization
    lr: float = 3e-4
    weight_decay: float = 1e-5
    lr_scheduler: Optional[str] = "cosine"  # None, "cosine", "plateau"
    warmup_steps: int = 1000
    
    # Validation
    val_freq: int = 10  # Validate every N episodes
    save_freq: int = 50  # Save model every N episodes
    
    # Checkpointing
    load_checkpoint: bool = False
    checkpoint_path: str = ""
    
    # Random seed
    seed: int = 42

@dataclass
class EnvConfig:
    """Configuration for the trading environment."""
    # Trading parameters
    initial_balance: float = 10000.0
    commission: float = 0.001  # 0.1% commission per trade
    max_position: float = 1.0  # Max position size as fraction of account
    
    # Reward shaping
    reward_scale: float = 1.0
    use_pnl: bool = True
    use_sharpe: bool = True
    use_drawdown: bool = False
    
    # Risk management
    stop_loss: Optional[float] = 0.05  # 5% stop loss
    take_profit: Optional[float] = 0.1  # 10% take profit
    max_drawdown: Optional[float] = 0.2  # 20% max drawdown

@dataclass
class LoggingConfig:
    """Configuration for logging and visualization."""
    log_dir: str = "logs"
    use_tensorboard: bool = True
    log_level: str = "INFO"
    save_metrics: bool = True
    save_freq: int = 10  # Save metrics every N episodes
    
    # Visualization
    plot_freq: int = 50  # Plot results every N episodes
    num_episodes_to_plot: int = 10  # Number of recent episodes to plot

@dataclass
class ExperimentConfig:
    """Top-level configuration for the experiment."""
    name: str = "trading_ppo"
    description: str = "Training PPO agent for crypto trading"
    tags: List[str] = field(default_factory=list)
    notes: str = ""
    
    # Project structure
    project_dir: str = os.getcwd()
    data_dir: str = os.path.join(project_dir, "data")
    model_dir: str = os.path.join(project_dir, "models")
    log_dir: str = os.path.join(project_dir, "logs")
    
    # Components
    data: DataConfig = field(default_factory=DataConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    training: TrainingConfig = field(default_factory=TrainingConfig)
    env: EnvConfig = field(default_factory=EnvConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    def __post_init__(self):
        # Ensure directories exist
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.model_dir, exist_ok=True)
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Update log_dir in logging config if using default
        if self.logging.log_dir == "logs":
            self.logging.log_dir = self.log_dir
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ExperimentConfig':
        """Create config from dictionary."""
        # Create base config
        config = cls()
        
        # Update with provided values
        for section, section_dict in config_dict.items():
            if hasattr(config, section):
                section_config = getattr(config, section)
                if isinstance(section_config, object):
                    # Update section config
                    for key, value in section_dict.items():
                        if hasattr(section_config, key):
                            setattr(section_config, key, value)
        
        return config
    
    @classmethod
    def from_yaml(cls, filepath: Union[str, Path]) -> 'ExperimentConfig':
        """Load config from YAML file."""
        with open(filepath, 'r') as f:
            config_dict = yaml.safe_load(f)
        return cls.from_dict(config_dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        config_dict = {}
        for section in ['data', 'model', 'training', 'env', 'logging']:
            section_config = getattr(self, section)
            config_dict[section] = {
                k: v for k, v in section_config.__dict__.items() 
                if not k.startswith('_')
            }
        
        # Add experiment-level settings
        config_dict.update({
            'name': self.name,
            'description': self.description,
            'tags': self.tags,
            'notes': self.notes,
            'project_dir': self.project_dir,
            'data_dir': self.data_dir,
            'model_dir': self.model_dir,
            'log_dir': self.log_dir
        })
        
        return config_dict
    
    def to_yaml(self, filepath: Union[str, Path]) -> None:
        """Save config to YAML file."""
        config_dict = self.to_dict()
        with open(filepath, 'w') as f:
            yaml.dump(config_dict, f, default_flow_style=False, sort_keys=False)

# Alias for backward compatibility
Config = ExperimentConfig
