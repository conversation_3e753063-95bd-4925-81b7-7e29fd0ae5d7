"""
Generate Enhanced Sample HTML Report
Create a sample of the new enhanced HTML report with all features
"""
import json
import numpy as np
from datetime import datetime, timedelta
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent))

from enhanced_html_report_generator import EnhancedHTMLReportGenerator


def create_enhanced_sample_data():
    """Create realistic sample data with all enhanced features."""
    
    # Generate sample composite scores
    np.random.seed(42)
    composite_scores = np.random.beta(2, 3, 1000) * 0.9 + 0.1  # Realistic distribution
    
    # Generate correlated returns
    returns = []
    for score in composite_scores:
        if score > 0.8:
            ret = np.random.normal(15, 8)
        elif score > 0.6:
            ret = np.random.normal(8, 12)
        elif score > 0.4:
            ret = np.random.normal(2, 15)
        else:
            ret = np.random.normal(-5, 18)
        returns.append(ret)
    
    # Generate trade counts
    trades = np.random.poisson(25, 1000)
    final_balances = [300 * (1 + ret/100) for ret in returns]
    
    # Create detailed best cycle data
    best_cycle_data = {
        'cycle': 847,
        'testing': {
            'composite_score': 0.9234,
            'total_return': 28.5,
            'final_balance': 385.50,
            'total_trades': 32,
            'winning_trades': 22,
            'losing_trades': 10,
            'max_drawdown': 0.082,
            'commission_paid': 12.45,
            'equity_curve': [300] + [300 + i*2.5 + np.random.normal(0, 5) for i in range(360)],
            'drawdown_curve': [max(0, np.random.exponential(0.02)) for _ in range(361)],
            'detailed_trades': [
                {
                    'trade_id': 1,
                    'direction': 'LONG',
                    'entry_time': datetime.now() - timedelta(hours=48),
                    'exit_time': datetime.now() - timedelta(hours=44),
                    'entry_price': 43250.00,
                    'exit_price': 43380.00,
                    'size': 0.006944,
                    'pnl_gross': 9.03,
                    'commission': 6.00,
                    'pnl_net': 3.03,
                    'return_pct': 1.01,
                    'duration_hours': 4.25,
                    'exit_reason': 'TAKE_PROFIT'
                },
                {
                    'trade_id': 2,
                    'direction': 'SHORT',
                    'entry_time': datetime.now() - timedelta(hours=40),
                    'exit_time': datetime.now() - timedelta(hours=38),
                    'entry_price': 43420.00,
                    'exit_price': 43280.00,
                    'size': 0.006912,
                    'pnl_gross': 9.67,
                    'commission': 6.00,
                    'pnl_net': 3.67,
                    'return_pct': 1.22,
                    'duration_hours': 2.0,
                    'exit_reason': 'TAKE_PROFIT'
                },
                {
                    'trade_id': 3,
                    'direction': 'LONG',
                    'entry_time': datetime.now() - timedelta(hours=36),
                    'exit_time': datetime.now() - timedelta(hours=35),
                    'entry_price': 43180.00,
                    'exit_price': 43120.00,
                    'size': 0.006956,
                    'pnl_gross': -4.17,
                    'commission': 6.00,
                    'pnl_net': -10.17,
                    'return_pct': -3.39,
                    'duration_hours': 1.0,
                    'exit_reason': 'STOP_LOSS'
                }
            ],
            'trade_actions': [
                {
                    'timestamp': datetime.now() - timedelta(hours=48),
                    'action': 'BUY',
                    'price': 43250.00,
                    'balance_before': 300.00,
                    'balance_after': 285.50
                },
                {
                    'timestamp': datetime.now() - timedelta(hours=44),
                    'action': 'SELL',
                    'price': 43380.00,
                    'balance_before': 285.50,
                    'balance_after': 303.03
                }
            ],
            'composite_metrics': {
                'win_rate': 0.583,
                'equity_growth': 0.125,
                'sortino_ratio': 2.4,
                'calmar_ratio': 2.1,
                'profit_factor': 1.7,
                'max_drawdown': 0.082,
                'risk_of_ruin': 0.021,
                'trade_frequency': 3.2
            }
        }
    }
    
    # Create complete analysis
    analysis = {
        'summary': {
            'total_cycles': 1000,
            'valid_cycles': 1000,
            'failed_cycles': 0,
            'success_rate': 100.0
        },
        'composite_scores': {
            'mean': np.mean(composite_scores),
            'std': np.std(composite_scores),
            'min': np.min(composite_scores),
            'max': np.max(composite_scores),
            'median': np.median(composite_scores)
        },
        'returns': {
            'mean': np.mean(returns),
            'std': np.std(returns),
            'min': np.min(returns),
            'max': np.max(returns),
            'median': np.median(returns),
            'positive_cycles': len([r for r in returns if r > 0]),
            'negative_cycles': len([r for r in returns if r < 0])
        },
        'trading_activity': {
            'mean_trades': np.mean(trades),
            'std_trades': np.std(trades),
            'min_trades': np.min(trades),
            'max_trades': np.max(trades)
        },
        'performance_categories': {
            'excellent': {'count': 47, 'percentage': 4.7},
            'good': {'count': 198, 'percentage': 19.8},
            'acceptable': {'count': 402, 'percentage': 40.2},
            'poor': {'count': 353, 'percentage': 35.3}
        },
        'composite_metrics_analysis': {
            'win_rate': {'mean': 0.52, 'std': 0.15, 'min': 0.20, 'max': 0.85, 'median': 0.51},
            'equity_growth': {'mean': 0.08, 'std': 0.12, 'min': -0.25, 'max': 0.35, 'median': 0.07},
            'sortino_ratio': {'mean': 1.2, 'std': 0.8, 'min': -0.5, 'max': 3.5, 'median': 1.1},
            'calmar_ratio': {'mean': 1.1, 'std': 0.9, 'min': -0.3, 'max': 4.2, 'median': 0.9},
            'profit_factor': {'mean': 1.3, 'std': 0.4, 'min': 0.6, 'max': 2.8, 'median': 1.2},
            'max_drawdown': {'mean': 0.18, 'std': 0.08, 'min': 0.05, 'max': 0.45, 'median': 0.16},
            'risk_of_ruin': {'mean': 0.15, 'std': 0.12, 'min': 0.01, 'max': 0.65, 'median': 0.12},
            'trade_frequency': {'mean': 3.2, 'std': 1.8, 'min': 0.5, 'max': 8.5, 'median': 2.9}
        },
        'best_cycles': [best_cycle_data] * 5,  # Top 5 cycles
        'raw_data': {
            'composite_scores': composite_scores.tolist(),
            'returns': returns,
            'trades': trades.tolist(),
            'final_balances': final_balances
        }
    }
    
    return analysis, [best_cycle_data]


def main():
    """Generate enhanced sample HTML report."""
    print("🎨 GENERATING ENHANCED SAMPLE HTML REPORT")
    print("=" * 60)
    print("📊 Creating realistic sample data with all enhanced features...")
    
    # Create sample data
    analysis, best_cycles = create_enhanced_sample_data()
    
    print(f"✅ Enhanced sample data created:")
    print(f"   Valid cycles: {analysis['summary']['valid_cycles']}")
    print(f"   Average composite score: {analysis['composite_scores']['mean']:.4f}")
    print(f"   Average return: {analysis['returns']['mean']:+.2f}%")
    print(f"   Best cycle trades: {len(best_cycles[0]['testing']['detailed_trades'])}")
    
    # Generate enhanced HTML report
    print(f"\n📄 Generating enhanced HTML report...")
    report_generator = EnhancedHTMLReportGenerator()
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_report = report_generator.generate_enhanced_report(analysis, best_cycles, timestamp)
    
    # Save enhanced report
    enhanced_report_path = "reports/ENHANCED_SAMPLE_1000_cycle_report.html"
    Path(enhanced_report_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(enhanced_report_path, 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print(f"✅ Enhanced HTML report generated!")
    print(f"📄 Saved to: {enhanced_report_path}")
    print(f"📊 Report size: {len(html_report):,} characters")
    
    # Also save sample data for reference
    sample_data_path = "reports/ENHANCED_SAMPLE_data.json"
    with open(sample_data_path, 'w') as f:
        json.dump({'analysis': analysis, 'best_cycles': best_cycles}, f, indent=2, default=str)
    
    print(f"📊 Sample data saved to: {sample_data_path}")
    
    print(f"\n🎉 ENHANCED SAMPLE REPORT READY!")
    print(f"📄 Open in browser: {enhanced_report_path}")
    print(f"📊 This shows the new enhanced features:")
    
    print(f"\n📋 NEW ENHANCED FEATURES:")
    print(f"   ✅ Trade-by-trade analysis with full details")
    print(f"   ✅ Buy/Sell/Hold action tracking")
    print(f"   ✅ Commission calculation (0.1% of trade size)")
    print(f"   ✅ Equity curve visualization")
    print(f"   ✅ Drawdown chart analysis")
    print(f"   ✅ Complete composite metrics breakdown")
    print(f"   ✅ Best cycle detailed analysis")
    print(f"   ✅ Performance insights and recommendations")


if __name__ == "__main__":
    main()
