# Binance API Configuration
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here
BINANCE_TESTNET=false

# Trading Configuration
INITIAL_CAPITAL=300.0
RISK_PER_TRADE=0.05
GRID_SPACING=0.0025
TAKE_PROFIT_MULTIPLIER=2.0
FEE_RATE=0.001

# Model Configuration
MODEL_PATH=models/live_model.pth
SYMBOL=BTCUSDT
TIMEFRAME=1h
LOOKBACK_PERIODS=24

# Risk Management
MAX_POSITIONS=5
STOP_LOSS_PCT=0.05
MAX_DAILY_TRADES=20
MAX_DAILY_LOSS_PCT=0.10

# Operational Settings
DECISION_INTERVAL=3600
HEALTH_CHECK_INTERVAL=300
LOG_LEVEL=INFO

# VPS Settings
VPS_MODE=true
AUTO_RESTART=true
MONITORING_ENABLED=true

# Alert Settings (Optional)
ALERT_EMAIL=<EMAIL>
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
