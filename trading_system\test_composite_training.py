"""
Test Composite Metrics Training
Validate the new composite reward system with proper metrics
"""
import asyncio
import sys
from pathlib import Path

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.ml.integrated_training import IntegratedTrainingPipeline, CompositeMetricsCalculator


async def test_composite_training():
    """Test the composite metrics training system."""
    print("🧪 TESTING COMPOSITE METRICS TRAINING")
    print("=" * 60)
    print("📊 Training toward composite score (0-1, where 1 is best)")
    print("=" * 60)
    
    # Test composite metrics calculator first
    print("\n🔧 TESTING COMPOSITE METRICS CALCULATOR")
    print("-" * 50)
    
    calculator = CompositeMetricsCalculator()
    
    # Display weights
    print("📊 Composite Weights:")
    total_weight = 0
    for metric, weight in calculator.weights.items():
        print(f"   {metric:<15}: {weight:.1%}")
        total_weight += weight
    print(f"   {'TOTAL':<15}: {total_weight:.1%}")
    
    # Display targets
    print(f"\n🎯 Target Values:")
    for metric, target in calculator.targets.items():
        if metric in ['win_rate', 'max_drawdown', 'risk_of_ruin']:
            print(f"   {metric:<15}: {target:.1%}")
        elif metric == 'trade_frequency':
            print(f"   {metric:<15}: {target:.1f} trades/day")
        else:
            print(f"   {metric:<15}: {target:.2f}")
    
    # Test metric normalization
    print(f"\n🔍 TESTING METRIC NORMALIZATION")
    print("-" * 40)
    
    test_cases = [
        ('win_rate', 0.60, 'Good win rate'),
        ('win_rate', 0.40, 'Poor win rate'),
        ('sortino_ratio', 2.5, 'Excellent Sortino'),
        ('sortino_ratio', 1.0, 'Poor Sortino'),
        ('max_drawdown', 0.10, 'Good drawdown'),
        ('max_drawdown', 0.25, 'Poor drawdown'),
        ('trade_frequency', 3.0, 'Good frequency'),
        ('trade_frequency', 10.0, 'Too many trades'),
        ('trade_frequency', 1.0, 'Too few trades')
    ]
    
    for metric, value, description in test_cases:
        normalized = calculator.normalize_metric(metric, value)
        print(f"   {metric:<15}: {value:6.2f} → {normalized:.3f} ({description})")
    
    # Test composite score calculation
    print(f"\n📊 TESTING COMPOSITE SCORE CALCULATION")
    print("-" * 40)
    
    # Scenario 1: Excellent performance
    excellent_metrics = {
        'win_rate': 0.65,
        'equity_growth': 0.12,
        'sortino_ratio': 2.8,
        'calmar_ratio': 2.5,
        'profit_factor': 1.8,
        'max_drawdown': 0.08,
        'risk_of_ruin': 0.005,
        'trade_frequency': 3.2
    }
    
    excellent_score = calculator.calculate_composite_score(excellent_metrics)
    print(f"🏆 Excellent Performance: {excellent_score:.3f}")
    
    # Scenario 2: Poor performance
    poor_metrics = {
        'win_rate': 0.35,
        'equity_growth': -0.05,
        'sortino_ratio': 0.5,
        'calmar_ratio': 0.3,
        'profit_factor': 0.8,
        'max_drawdown': 0.25,
        'risk_of_ruin': 0.15,
        'trade_frequency': 0.5
    }
    
    poor_score = calculator.calculate_composite_score(poor_metrics)
    print(f"❌ Poor Performance: {poor_score:.3f}")
    
    # Scenario 3: Balanced performance
    balanced_metrics = {
        'win_rate': 0.55,
        'equity_growth': 0.08,
        'sortino_ratio': 1.8,
        'calmar_ratio': 1.5,
        'profit_factor': 1.3,
        'max_drawdown': 0.12,
        'risk_of_ruin': 0.02,
        'trade_frequency': 3.5
    }
    
    balanced_score = calculator.calculate_composite_score(balanced_metrics)
    print(f"⚖️  Balanced Performance: {balanced_score:.3f}")
    
    # Test training pipeline
    print(f"\n🚀 TESTING TRAINING PIPELINE")
    print("-" * 40)
    
    # Initialize training pipeline
    pipeline = IntegratedTrainingPipeline(initial_balance=300.0)
    
    print(f"✅ Training pipeline initialized")
    print(f"📊 Optimizing for composite score (0-1 range)")
    
    # Test data collection
    print(f"\n📈 Testing data collection...")
    try:
        training_data = await pipeline.collect_training_data(days=7)  # Small test
        print(f"✅ Collected {len(training_data)} candles")
        
        # Test episode simulation with composite rewards
        print(f"\n🎮 Testing episode simulation with composite rewards...")
        episode_data = await pipeline.simulate_episode(training_data)
        
        print(f"✅ Episode simulation completed")
        print(f"📊 Final balance: ${episode_data['final_balance']:.2f}")
        print(f"📊 Total return: {episode_data['total_return']:+.2f}%")
        print(f"📊 Composite score: {episode_data['composite_score']:.4f}")
        print(f"📊 Number of trades: {len(episode_data['trades'])}")
        
        # Display composite metrics breakdown
        metrics = episode_data['composite_metrics']
        print(f"\n📋 COMPOSITE METRICS BREAKDOWN:")
        for metric_name, value in metrics.items():
            normalized = calculator.normalize_metric(metric_name, value)
            weight = calculator.weights[metric_name]
            contribution = normalized * weight
            
            if metric_name in ['win_rate', 'max_drawdown', 'risk_of_ruin']:
                print(f"   {metric_name:<15}: {value:6.1%} → {normalized:.3f} × {weight:.1%} = {contribution:.4f}")
            elif metric_name == 'trade_frequency':
                print(f"   {metric_name:<15}: {value:6.1f} → {normalized:.3f} × {weight:.1%} = {contribution:.4f}")
            else:
                print(f"   {metric_name:<15}: {value:6.2f} → {normalized:.3f} × {weight:.1%} = {contribution:.4f}")
        
        print(f"   {'TOTAL SCORE':<15}: {episode_data['composite_score']:.4f}")
        
        # Test quick training (2 episodes)
        print(f"\n🎯 Testing quick training with composite rewards...")
        history = await pipeline.train_model(episodes=2)
        
        print(f"✅ Training test completed!")
        print(f"📊 Episodes trained: {len(history)}")
        
        if history:
            for i, episode in enumerate(history):
                print(f"   Episode {episode['episode']}: "
                      f"Composite={episode['composite_score']:.4f}, "
                      f"Balance=${episode['final_balance']:.2f}")
        
        print(f"\n🎉 ALL COMPOSITE TRAINING TESTS PASSED!")
        print(f"✅ Training optimized for composite metrics")
        print(f"✅ Rewards shaped based on 8 key performance indicators")
        print(f"✅ Model will learn to maximize composite score (0-1)")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    success = await test_composite_training()
    
    if success:
        print(f"\n🚀 COMPOSITE TRAINING SYSTEM READY!")
        print(f"📊 Training optimized for:")
        print(f"   • Win Rate (22% weight)")
        print(f"   • Equity Growth (20% weight)")
        print(f"   • Sortino Ratio (18% weight)")
        print(f"   • Calmar Ratio (15% weight)")
        print(f"   • Profit Factor (10% weight)")
        print(f"   • Max Drawdown (8% weight)")
        print(f"   • Risk of Ruin (5% weight)")
        print(f"   • Trade Frequency (2% weight)")
        print(f"\n🎯 Ready for 1000-cycle evaluation!")
    else:
        print(f"\n❌ Issues found - need to fix before proceeding")


if __name__ == "__main__":
    asyncio.run(main())
