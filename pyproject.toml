[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "trading-system"
version = "0.1.0"
description = "Grid Trading System with TCN-CNN-PPO"
authors = [
    {name = "Trading Team"}
]
requires-python = ">=3.9"
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]
dependencies = [
    "aiohttp>=3.8.0",
    "pandas>=1.3.0",
    "pydantic>=1.9.0",
    "python-dotenv>=0.19.0",
    "tenacity>=8.0.0",
    "numpy>=1.20.0",
    "python-dateutil>=2.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-asyncio>=0.15.0",
    "pytest-cov>=2.0.0",
    "black>=21.0",
    "isort>=5.0.0",
    "mypy>=0.9.0",
    "types-python-dateutil",
    "types-pytz",
]

[tool.black]
line-length = 120
target-version = ['py39']
include = '\.pyi?$'
