"""
Test cases for the GridTradingEnv class using real market data.
"""
import asyncio
import sys
import pytest
import pytest_asyncio
import numpy as np
from datetime import datetime, timedelta, timezone

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from src.trading.environment import GridTradingEnv, Action
from src.data.models import OHLCVSchema
from src.data.binance_fetcher import BinanceDataFetcher

@pytest.fixture
def env():
    """Fixture to create a GridTradingEnv instance for testing."""
    return GridTradingEnv(
        initial_balance=10000.0,
        risk_per_trade=0.05,  # 5% risk per trade
        grid_spacing=0.01,    # 1% grid spacing for testing
        take_profit_multiplier=2.0,
        max_grid_levels=20,
        fee_rate=0.001
    )

@pytest.fixture
def sample_ohlcv():
    """Fixture to create sample OHLCV data."""
    return OHLCVSchema(
        timestamp=datetime.now(),
        open=50000.0,
        high=50500.0,
        low=49500.0,
        close=50250.0,
        volume=100.0
    )

def test_initialization(env):
    """Test environment initialization."""
    assert env.balance == 10000.0
    assert env.risk_per_trade == 0.05
    assert len(env.positions) == 0
    assert len(env.grid_levels) == 0

def test_reset(env):
    """Test environment reset."""
    initial_price = 50000.0
    timestamp = datetime.now()
    state = env.reset(initial_price, timestamp)

    assert env.current_price == initial_price
    assert env.current_time == timestamp
    assert len(env.grid_levels) > 0
    assert isinstance(state, np.ndarray)

def test_grid_initialization(env):
    """Test grid levels initialization."""
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    # Should have grid levels around the initial price
    assert len(env.grid_levels) > 0
    assert any(level.price == initial_price for level in env.grid_levels)

    # Grid levels should be sorted by price
    prices = [level.price for level in env.grid_levels]
    assert prices == sorted(prices)

def test_long_position(env, sample_ohlcv):
    """Test opening and managing a long position."""
    # Reset environment
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    # Open long position
    state, reward, done, info = env.step(
        Action.BUY,
        initial_price,
        datetime.now(),
        sample_ohlcv
    )

    # Should have one open position
    assert len(env.positions) == 1
    position = env.positions[0]
    assert position.position_type == 'long'
    assert position.entry_price == initial_price

    # Position size should be based on risk
    expected_size = (env.balance * env.risk_per_trade) / (initial_price * 0.005)  # 0.5% stop loss
    assert abs(position.size - expected_size) < 0.01

    # Take profit should be 1% above entry (0.5% stop loss * 2 multiplier)
    assert position.take_profit == initial_price * 1.01

    # Stop loss should be 0.5% below entry
    assert position.stop_loss == initial_price * 0.995

def test_short_position(env, sample_ohlcv):
    """Test opening and managing a short position."""
    # Reset environment
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    # Open short position
    state, reward, done, info = env.step(
        Action.SELL,
        initial_price,
        datetime.now(),
        sample_ohlcv
    )

    # Should have one open position
    assert len(env.positions) == 1
    position = env.positions[0]
    assert position.position_type == 'short'
    assert position.entry_price == initial_price

    # Take profit should be 1% below entry
    assert position.take_profit == initial_price * 0.99

    # Stop loss should be 0.5% above entry
    assert position.stop_loss == initial_price * 1.005

def test_position_pnl(env):
    """Test position P&L calculation."""
    # Reset environment
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    # Open long position
    env.step(Action.BUY, initial_price, datetime.now())
    position = env.positions[0]

    # Update price up by 0.5%
    new_price = initial_price * 1.005
    position.update(new_price)

    # P&L should be positive
    assert position.pnl > 0
    assert position.pnl_pct > 0

    # Update price down by 1% (below entry)
    new_price = initial_price * 0.99
    position.update(new_price)

    # P&L should be negative
    assert position.pnl < 0
    assert position.pnl_pct < 0

def test_position_stop_loss(env):
    """Test position stop loss triggering."""
    # Reset environment
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    # Open long position
    env.step(Action.BUY, initial_price, datetime.now())

    # Move price to trigger stop loss
    stop_loss_price = initial_price * 0.995  # 0.5% stop loss
    state, reward, done, info = env.step(
        Action.HOLD,
        stop_loss_price,
        datetime.now()
    )

    # Position should be closed
    assert len(env.positions) == 0
    assert len(env.closed_positions) == 1

    # Balance should be updated
    assert env.balance < env.initial_balance  # Should have a loss

def test_position_take_profit(env):
    """Test position take profit triggering."""
    # Reset environment
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    # Open long position
    env.step(Action.BUY, initial_price, datetime.now())

    # Move price to trigger take profit
    take_profit_price = initial_price * 1.01  # 1% take profit
    state, reward, done, info = env.step(
        Action.HOLD,
        take_profit_price,
        datetime.now()
    )

    # Position should be closed
    assert len(env.positions) == 0
    assert len(env.closed_positions) == 1

    # Balance should be updated with profit
    assert env.balance > env.initial_balance

def test_grid_level_management(env):
    """Test dynamic grid level management."""
    # Reset environment
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    initial_levels = len(env.grid_levels)

    # Move price up to trigger grid expansion
    new_price = env.grid_levels[-1].price * 1.05  # Above highest level
    env.step(Action.HOLD, new_price, datetime.now())

    # Should have added new levels
    assert len(env.grid_levels) > initial_levels

    # Move price down to trigger grid expansion in the other direction
    new_price = env.grid_levels[0].price * 0.95  # Below lowest level
    env.step(Action.HOLD, new_price, datetime.now())

    # Should maintain grid size within max_grid_levels
    assert len(env.grid_levels) <= env.max_grid_levels

def test_metrics_calculation(env):
    """Test calculation of trading metrics."""
    # Reset environment
    initial_price = 50000.0
    env.reset(initial_price, datetime.now())

    # Make some trades
    env.step(Action.BUY, initial_price, datetime.now())
    env.step(Action.HOLD, initial_price * 1.01, datetime.now())  # Trigger take profit

    # Get metrics
    metrics = env.get_metrics()

    # Check metrics
    assert 'balance' in metrics
    assert 'equity' in metrics
    assert 'return_pct' in metrics
    assert 'max_drawdown' in metrics
    assert 'trades' in metrics
    assert 'win_rate' in metrics
    assert 'sharpe_ratio' in metrics

    # Should have made a profit
    assert metrics['return_pct'] > 0
    assert metrics['win_rate'] == 100.0  # 1 win, 0 losses

def test_position_ttl(env):
    """Test position TTL (time to live) functionality."""
    # Reset environment with short TTL
    env.position_ttl = 1  # 1 hour TTL
    now = datetime.now()
    env.reset(50000.0, now)

    # Open position
    env.step(Action.BUY, 50000.0, now)
    assert len(env.positions) == 1

    # Move time forward past TTL
    future_time = now + timedelta(hours=2)
    env.step(Action.HOLD, 50000.0, future_time)

    # Position should be closed due to TTL
    assert len(env.positions) == 0
    assert len(env.closed_positions) == 1
