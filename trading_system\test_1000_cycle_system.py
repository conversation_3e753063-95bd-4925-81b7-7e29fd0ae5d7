"""
Test 1000-Cycle Evaluation System
Quick validation before running the full 1000 cycles
"""
import asyncio
import sys
from pathlib import Path

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from comprehensive_1000_cycle_evaluation import Comprehensive1000CycleEvaluator


async def test_1000_cycle_system():
    """Test the 1000-cycle evaluation system with a small sample."""
    print("🧪 TESTING 1000-CYCLE EVALUATION SYSTEM")
    print("=" * 60)
    print("📊 Quick validation with 5 cycles")
    print("=" * 60)
    
    # Initialize evaluator
    evaluator = Comprehensive1000CycleEvaluator(initial_capital=300.0)
    
    # Override for quick test
    evaluator.total_cycles = 5
    evaluator.training_days = 7  # Reduced for testing
    evaluator.test_days = 3     # Reduced for testing
    
    print(f"✅ Evaluator initialized:")
    print(f"   Training: {evaluator.training_days} days")
    print(f"   Testing: {evaluator.test_days} days")
    print(f"   Cycles: {evaluator.total_cycles}")
    
    # Test data collection
    print(f"\n📈 Testing data collection...")
    try:
        data_splits = await evaluator.collect_evaluation_data()
        print(f"✅ Data collection successful:")
        print(f"   Training data: {len(data_splits['training'])} candles")
        print(f"   Testing data: {len(data_splits['testing'])} candles")
        
    except Exception as e:
        print(f"❌ Data collection failed: {e}")
        return False
    
    # Test single cycle
    print(f"\n🎮 Testing single cycle...")
    try:
        cycle_result = await evaluator.run_single_cycle(0, data_splits)
        print(f"✅ Single cycle completed:")
        print(f"   Training best score: {cycle_result['training']['best_composite_score']:.4f}")
        print(f"   Testing composite score: {cycle_result['testing']['composite_score']:.4f}")
        print(f"   Testing return: {cycle_result['testing']['total_return']:+.2f}%")
        print(f"   Testing trades: {cycle_result['testing']['total_trades']}")
        
    except Exception as e:
        print(f"❌ Single cycle failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test full evaluation (5 cycles)
    print(f"\n🚀 Testing full evaluation (5 cycles)...")
    try:
        results = await evaluator.run_comprehensive_evaluation()
        print(f"✅ Full evaluation completed:")
        print(f"   Total results: {len(results)}")
        
        # Test analysis
        print(f"\n📊 Testing analysis...")
        analysis = evaluator.analyze_results(results)
        
        if 'error' in analysis:
            print(f"❌ Analysis failed: {analysis['error']}")
            return False
        
        print(f"✅ Analysis completed:")
        print(f"   Valid cycles: {analysis['summary']['valid_cycles']}")
        print(f"   Average composite score: {analysis['composite_scores']['mean']:.4f}")
        print(f"   Average return: {analysis['returns']['mean']:+.2f}%")
        
        # Test HTML report generation
        print(f"\n📄 Testing HTML report generation...")
        html_report = evaluator.generate_comprehensive_html_report(analysis, "test")
        
        if len(html_report) > 1000:  # Basic check
            print(f"✅ HTML report generated ({len(html_report)} characters)")
        else:
            print(f"❌ HTML report too short")
            return False
        
        # Save test report
        test_report_path = "reports/test_1000_cycle_report.html"
        Path(test_report_path).parent.mkdir(parents=True, exist_ok=True)
        with open(test_report_path, 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        print(f"📄 Test report saved to: {test_report_path}")
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ 1000-cycle system is ready for full evaluation")
        
        return True
        
    except Exception as e:
        print(f"❌ Full evaluation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    success = await test_1000_cycle_system()
    
    if success:
        print(f"\n🚀 READY FOR FULL 1000-CYCLE EVALUATION!")
        print(f"📊 System validated and working correctly")
        print(f"🎯 Run: python comprehensive_1000_cycle_evaluation.py")
        print(f"\n⏱️  Estimated time for 1000 cycles: 2-4 hours")
        print(f"📈 Expected outputs:")
        print(f"   • JSON results file")
        print(f"   • JSON analysis file") 
        print(f"   • Comprehensive HTML report")
        print(f"   • Detailed performance metrics")
        print(f"   • Interactive charts and visualizations")
    else:
        print(f"\n❌ Issues found - need to fix before running 1000 cycles")


if __name__ == "__main__":
    asyncio.run(main())
