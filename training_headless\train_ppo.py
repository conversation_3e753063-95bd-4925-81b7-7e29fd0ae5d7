import os
import time
import numpy as np
import torch
import torch.optim as optim
from torch.utils.tensorboard import SummaryWriter
from datetime import datetime
import random
from typing import Dict, List, Tuple, Optional, Any
import json
import argparse

from src.agent.ppo_agent import PPOAgent
from src.utils.logger import setup_logger
from src.data.data_loader import CryptoDataLoader
from src.env.trading_env import CryptoTradingEnv
from src.config import Config

# Set up logger
logger = setup_logger(__name__)

class PPOTrainer:
    """PPO Trainer for training the trading agent."""
    
    def __init__(self, config_path: str):
        """
        Initialize the PPO trainer.
        
        Args:
            config_path: Path to configuration file
        """
        # Load configuration
        self.config = Config.from_file(config_path)
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Set random seeds for reproducibility
        self._set_seeds(self.config.training.seed)
        
        # Set up logging
        self._setup_logging()
        
        # Initialize data loader
        self.data_loader = CryptoDataLoader(self.config.data)
        
        # Create training and validation environments
        self.train_env = CryptoTradingEnv(
            data=self.data_loader.train_data,
            window_size=self.config.model.seq_len,
            frame_bound=(self.config.model.seq_len, len(self.data_loader.train_data)),
            **self.config.env
        )
        
        self.val_env = CryptoTradingEnv(
            data=self.data_loader.val_data,
            window_size=self.config.model.seq_len,
            frame_bound=(self.config.model.seq_len, len(self.data_loader.val_data)),
            **self.config.env
        )
        
        # Get observation and action dimensions from environment
        obs_dim = self.train_env.observation_space.shape[0]
        action_dim = self.train_env.action_space.n
        
        # Initialize PPO agent
        self.agent = PPOAgent(
            obs_dim=obs_dim,
            action_dim=action_dim,
            seq_len=self.config.model.seq_len,
            lr=self.config.training.lr,
            gamma=self.config.training.gamma,
            gae_lambda=self.config.training.gae_lambda,
            clip_eps=self.config.training.clip_eps,
            entropy_coef=self.config.training.entropy_coef,
            value_coef=self.config.training.value_coef,
            max_grad_norm=self.config.training.max_grad_norm,
            device=self.device,
            **self.config.model.kwargs
        )
        
        # Load checkpoint if provided
        if self.config.training.load_checkpoint:
            self._load_checkpoint(self.config.training.checkpoint_path)
        
        logger.info(f"Initialized PPO trainer with config: {self.config}")
    
    def _set_seeds(self, seed: int) -> None:
        """Set random seeds for reproducibility."""
        torch.manual_seed(seed)
        np.random.seed(seed)
        random.seed(seed)
        if torch.cuda.is_available():
            torch.cuda.manual_seed_all(seed)
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
    
    def _setup_logging(self) -> None:
        """Set up logging and tensorboard writer."""
        # Create experiment directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_dir = os.path.join(
            self.config.logging.log_dir,
            f"{self.config.experiment.name}_{timestamp}"
        )
        os.makedirs(self.log_dir, exist_ok=True)
        
        # Save config
        config_path = os.path.join(self.log_dir, 'config.json')
        with open(config_path, 'w') as f:
            json.dump(self.config.to_dict(), f, indent=2)
        
        # Initialize tensorboard writer
        self.writer = SummaryWriter(log_dir=self.log_dir)
        
        logger.info(f"Logging to {self.log_dir}")
    
    def train(self) -> None:
        """Train the PPO agent."""
        logger.info("Starting training...")
        
        # Training loop
        global_step = 0
        best_val_reward = -float('inf')
        
        for episode in range(1, self.config.training.num_episodes + 1):
            # Training phase
            train_metrics = self._train_episode()
            
            # Log training metrics
            for k, v in train_metrics.items():
                self.writer.add_scalar(f'train/{k}', v, global_step)
            
            # Validation phase
            if episode % self.config.training.val_freq == 0:
                val_metrics = self._evaluate()
                
                # Log validation metrics
                for k, v in val_metrics.items():
                    self.writer.add_scalar(f'val/{k}', v, global_step)
                
                # Save best model
                if val_metrics['episode_reward'] > best_val_reward:
                    best_val_reward = val_metrics['episode_reward']
                    self._save_checkpoint('best')
                    logger.info(f"New best model saved with reward: {best_val_reward:.2f}")
            
            # Save checkpoint periodically
            if episode % self.config.training.save_freq == 0:
                self._save_checkpoint(f'episode_{episode}')
            
            # Log episode summary
            logger.info(
                f"Episode {episode}/{self.config.training.num_episodes} | "
                f"Train Reward: {train_metrics['episode_reward']:.2f} | "
                f"Val Reward: {val_metrics.get('episode_reward', 0):.2f} | "
                f"Steps: {train_metrics['episode_steps']} | "
                f"Time: {train_metrics['episode_time']:.1f}s"
            )
            
            global_step += 1
        
        # Save final model
        self._save_checkpoint('final')
        logger.info("Training completed!")
    
    def _train_episode(self) -> Dict[str, float]:
        """Run one training episode."""
        start_time = time.time()
        state = self.train_env.reset()
        episode_reward = 0
        episode_steps = 0
        done = False
        
        while not done:
            # Select action
            action, log_prob, value = self.agent.act(state)
            
            # Take step in environment
            next_state, reward, done, info = self.train_env.step(action)
            
            # Store transition
            self.agent.store_transition(
                state=state,
                action=action,
                log_prob=log_prob,
                reward=reward,
                value=value,
                done=done
            )
            
            # Update agent if enough samples are collected
            if len(self.agent.buffer) >= self.config.training.batch_size or done:
                metrics = self.agent.update(next_state, done)
                
                # Log metrics
                for k, v in metrics.items():
                    self.writer.add_scalar(f'train_metrics/{k}', v, episode_steps)
            
            # Update state and counters
            state = next_state
            episode_reward += reward
            episode_steps += 1
        
        # Calculate episode metrics
        episode_time = time.time() - start_time
        
        return {
            'episode_reward': episode_reward,
            'episode_steps': episode_steps,
            'episode_time': episode_time,
            'portfolio_value': info.get('portfolio_value', 0),
            'sharpe_ratio': info.get('sharpe_ratio', 0),
            'max_drawdown': info.get('max_drawdown', 0)
        }
    
    def _evaluate(self) -> Dict[str, float]:
        """Evaluate the agent on the validation set."""
        self.agent.eval_mode()
        
        state = self.val_env.reset()
        episode_reward = 0
        episode_steps = 0
        done = False
        
        while not done:
            # Select action deterministically for evaluation
            action, _, _ = self.agent.act(state, deterministic=True)
            
            # Take step in environment
            next_state, reward, done, info = self.val_env.step(action)
            
            # Update state and counters
            state = next_state
            episode_reward += reward
            episode_steps += 1
        
        # Calculate evaluation metrics
        metrics = {
            'episode_reward': episode_reward,
            'episode_steps': episode_steps,
            'portfolio_value': info.get('portfolio_value', 0),
            'sharpe_ratio': info.get('sharpe_ratio', 0),
            'max_drawdown': info.get('max_drawdown', 0),
            'win_rate': info.get('win_rate', 0),
            'profit_factor': info.get('profit_factor', 0)
        }
        
        self.agent.train_mode()
        return metrics
    
    def _save_checkpoint(self, name: str) -> None:
        """Save model checkpoint."""
        checkpoint_path = os.path.join(self.log_dir, f"{name}.pth")
        self.agent.save(checkpoint_path)
        logger.info(f"Saved checkpoint to {checkpoint_path}")
    
    def _load_checkpoint(self, path: str) -> None:
        """Load model checkpoint."""
        if os.path.exists(path):
            self.agent.load(path)
            logger.info(f"Loaded checkpoint from {path}")
        else:
            logger.warning(f"Checkpoint not found at {path}, starting from scratch")

def parse_args():
    parser = argparse.ArgumentParser(description='Train a PPO agent for crypto trading')
    parser.add_argument('--config', type=str, default='configs/default.yaml',
                        help='Path to config file')
    parser.add_argument('--resume', type=str, default=None,
                        help='Path to checkpoint to resume training')
    return parser.parse_args()

def main():
    args = parse_args()
    
    try:
        # Initialize trainer
        trainer = PPOTrainer(args.config)
        
        # Start training
        trainer.train()
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
    except Exception as e:
        logger.exception(f"Training failed: {str(e)}")
        raise

if __name__ == "__main__":
    main()
