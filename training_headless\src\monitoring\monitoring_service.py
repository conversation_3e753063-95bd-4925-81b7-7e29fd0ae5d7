"""
Monitoring Service for Grid Trading Bot
"""
import asyncio
import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from .health_check import HealthMonitor, HealthStatus
from .alert_manager import AlertManager

# Configure logging
logger = logging.getLogger(__name__)

class MonitoringService:
    """Service for continuous system monitoring and health checks"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.health_monitor = HealthMonitor(config)
        self.alert_manager = AlertManager(config)
        self.running = False
        self.check_interval = config.get('monitoring', {}).get('check_interval', 60)  # seconds
        
    async def start(self):
        """Start the monitoring service"""
        if self.running:
            logger.warning("Monitoring service is already running")
            return
            
        self.running = True
        logger.info("Starting monitoring service...")
        
        # Initial health check
        await self._run_health_checks()
        
        # Start the monitoring loop
        while self.running:
            try:
                await asyncio.sleep(self.check_interval)
                await self._run_health_checks()
            except asyncio.CancelledError:
                logger.info("Monitoring service stopping...")
                self.running = False
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}", exc_info=True)
                await asyncio.sleep(5)  # Prevent tight loop on errors
    
    async def stop(self):
        """Stop the monitoring service"""
        self.running = False
        
    async def _run_health_checks(self):
        """Run health checks and handle alerts"""
        try:
            # Run all health checks
            health_status = await self.health_monitor.run_health_checks()
            
            # Process each component's status
            for component, status in health_status.items():
                if status.status != 'healthy':
                    # Send alert for unhealthy components
                    await self.alert_manager.send_alert(
                        alert_type=f"{component}_health",
                        message=f"{component} is {status.status}: {status.message}",
                        severity='critical' if status.status == 'critical' else 'warning',
                        data={
                            'component': component,
                            'status': status.status,
                            'metrics': status.metrics or {}
                        }
                    )
                    
                    # Try to auto-fix issues
                    if status.status != 'healthy':
                        fixes = await self.health_monitor.auto_fix_issues(health_status)
                        if fixes:
                            logger.info(f"Applied auto-fixes: {fixes}")
                            
            # Log overall status periodically
            logger.info("Health check completed")
            
        except Exception as e:
            logger.error(f"Error running health checks: {e}", exc_info=True)
            await self.alert_manager.send_alert(
                alert_type="monitoring_error",
                message=f"Error running health checks: {str(e)}",
                severity='critical'
            )
    
    def get_status(self) -> Dict[str, Any]:
        """Get current system status"""
        return {
            'running': self.running,
            'last_check': self.health_monitor.last_check.isoformat() if hasattr(self.health_monitor, 'last_check') else None,
            'next_check_in': (datetime.utcnow() + timedelta(seconds=self.check_interval)).isoformat() if self.running else None
        }


async def run_monitoring_service(config: Dict[str, Any]):
    """Run the monitoring service as a standalone application"""
    service = MonitoringService(config)
    
    # Handle graceful shutdown
    def handle_shutdown(signum, frame):
        logger.info("Shutdown signal received, stopping monitoring service...")
        asyncio.create_task(service.stop())
    
    import signal
    signal.signal(signal.SIGINT, handle_shutdown)
    signal.signal(signal.SIGTERM, handle_shutdown)
    
    try:
        await service.start()
    except Exception as e:
        logger.critical(f"Monitoring service failed: {e}", exc_info=True)
        raise
