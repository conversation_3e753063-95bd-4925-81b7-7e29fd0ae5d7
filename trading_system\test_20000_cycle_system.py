"""
Test 20,000-Cycle System
Quick validation before running the full 20,000-cycle evaluation
"""
import asyncio
import sys
from pathlib import Path

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from run_20000_cycle_evaluation import Comprehensive20000CycleEvaluator


async def test_20000_cycle_system():
    """Test the 20,000-cycle system with a small sample."""
    print("🧪 TESTING 20,000-CYCLE SYSTEM")
    print("=" * 60)
    print("📊 Running 5-cycle test to validate all components")
    print("=" * 60)
    
    # Initialize evaluator
    evaluator = Comprehensive20000CycleEvaluator(initial_capital=300.0)
    evaluator.total_cycles = 5  # Test with just 5 cycles
    
    print(f"✅ Test evaluator configured:")
    print(f"   Training: {evaluator.training_days} days")
    print(f"   Testing: {evaluator.test_days} days")
    print(f"   Cycles: {evaluator.total_cycles}")
    print(f"   Initial capital: ${evaluator.initial_capital}")
    
    try:
        # Test data collection
        print(f"\n📊 Testing data collection...")
        data_splits = await evaluator.collect_evaluation_data()
        print(f"✅ Data collected successfully:")
        print(f"   Training candles: {len(data_splits['training'])}")
        print(f"   Testing candles: {len(data_splits['testing'])}")
        
        # Test single cycle
        print(f"\n🔄 Testing single cycle...")
        cycle_result = await evaluator.run_single_cycle(0, data_splits)
        print(f"✅ Single cycle completed:")
        print(f"   Composite score: {cycle_result.get('testing', {}).get('composite_score', 0):.4f}")
        print(f"   Final balance: ${cycle_result.get('testing', {}).get('final_balance', 300):.2f}")
        print(f"   Total trades: {cycle_result.get('testing', {}).get('total_trades', 0)}")
        
        # Test analysis
        print(f"\n📊 Testing analysis...")
        analysis = evaluator.analyze_results([cycle_result])
        print(f"✅ Analysis completed:")
        print(f"   Valid cycles: {analysis['summary']['valid_cycles']}")
        print(f"   Average composite: {analysis['composite_scores']['mean']:.4f}")
        
        # Test live trading readiness
        print(f"\n🚀 Testing live trading readiness...")
        readiness = evaluator.calculate_live_trading_readiness(analysis)
        print(f"✅ Readiness assessment completed:")
        print(f"   Stability score: {readiness['stability_score']:.1f}/100")
        print(f"   Consistency: {readiness['consistency_rating']}")
        print(f"   Risk level: {readiness['risk_assessment']}")
        print(f"   Recommendation: {readiness['recommendation']}")
        
        # Test HTML report generation
        print(f"\n📄 Testing HTML report generation...")
        from enhanced_html_report_generator import EnhancedHTMLReportGenerator
        
        analysis['live_trading_readiness'] = readiness
        report_generator = EnhancedHTMLReportGenerator()
        best_cycles = analysis.get('best_cycles', [])
        html_report = report_generator.generate_enhanced_report(analysis, best_cycles, "test")
        
        # Save test report
        test_report_file = "reports/test_20000_cycle_system.html"
        Path(test_report_file).parent.mkdir(parents=True, exist_ok=True)
        with open(test_report_file, 'w', encoding='utf-8') as f:
            f.write(html_report)
        
        print(f"✅ HTML report generated: {test_report_file}")
        
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ 20,000-cycle system is ready for full evaluation")
        print(f"✅ All components working correctly:")
        print(f"   • Data collection ✅")
        print(f"   • Cycle execution ✅") 
        print(f"   • Analysis ✅")
        print(f"   • Live trading readiness ✅")
        print(f"   • HTML report generation ✅")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    success = await test_20000_cycle_system()
    
    if success:
        print(f"\n🚀 READY TO RUN 20,000-CYCLE EVALUATION!")
        print(f"📊 Execute: python run_20000_cycle_evaluation.py")
        print(f"⏱️ Estimated time: 8-12 hours for full evaluation")
        print(f"📄 Will generate comprehensive HTML report with:")
        print(f"   • Equity curves and drawdown charts")
        print(f"   • Trade-by-trade analysis")
        print(f"   • Full composite metrics")
        print(f"   • Live trading readiness assessment")
    else:
        print(f"\n❌ SYSTEM NOT READY")
        print(f"🔧 Please fix the issues above before running full evaluation")


if __name__ == "__main__":
    asyncio.run(main())
