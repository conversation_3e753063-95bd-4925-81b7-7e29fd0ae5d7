"""
Complete system integration test using real market data.
This test validates the entire trading pipeline from data fetching to trading execution.
"""
import asyncio
import sys
from datetime import datetime, timedelta, timezone

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action


async def test_complete_trading_system():
    """Test the complete trading system with real market data."""
    print("🚀 Starting Complete Trading System Test")
    print("=" * 50)

    # Step 1: Fetch real market data
    print("\n📊 Step 1: Fetching real market data...")
    async with BinanceDataFetcher() as fetcher:
        # Get last 24 hours of BTC/USDT 1h data
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=24)

        response = await fetcher.fetch_historical_data(
            symbol="BTCUSDT",
            interval="1h",
            start_time=start_time,
            end_time=end_time
        )

        if not response.success:
            print(f"❌ Failed to fetch data: {response.error}")
            return False

        market_data = response.data
        print(f"✅ Successfully fetched {len(market_data)} candles")
        print(f"   Time range: {market_data[0].timestamp} to {market_data[-1].timestamp}")
        print(f"   Price range: ${market_data[0].low:.2f} - ${max(c.high for c in market_data):.2f}")

    # Step 2: Initialize trading environment
    print("\n🎯 Step 2: Initializing trading environment...")
    env = GridTradingEnv(
        initial_balance=10000.0,
        risk_per_trade=0.05,  # 5% risk per trade
        grid_spacing=0.0025,  # 0.25% grid spacing
        take_profit_multiplier=2.0,  # 2:1 risk-reward
        fee_rate=0.001  # 0.1% trading fee
    )

    # Reset environment with first candle
    first_candle = market_data[0]
    state = env.reset(first_candle.close, first_candle.timestamp)
    print(f"✅ Environment initialized with price ${first_candle.close:.2f}")
    print(f"   Initial balance: ${env.balance:.2f}")
    print(f"   Grid levels: {len(env.grid_levels)}")

    # Step 3: Simulate trading through historical data
    print("\n📈 Step 3: Simulating trading through historical data...")
    trades_executed = 0
    max_trades = 5  # Limit trades for testing

    for i, candle in enumerate(market_data[1:], 1):
        # Simple trading logic: buy on price dips, sell on price spikes
        price_change = (candle.close - market_data[i-1].close) / market_data[i-1].close

        action = Action.HOLD
        if len(env.positions) == 0 and trades_executed < max_trades:
            if price_change < -0.01:  # Price dropped more than 1%
                action = Action.BUY
                trades_executed += 1
                print(f"   🟢 BUY signal at ${candle.close:.2f} (drop: {price_change:.2%})")
            elif price_change > 0.01:  # Price rose more than 1%
                action = Action.SELL
                trades_executed += 1
                print(f"   🔴 SELL signal at ${candle.close:.2f} (rise: {price_change:.2%})")

        # Execute step
        state, reward, done, info = env.step(action, candle.close, candle.timestamp, candle)

        # Log position updates
        last_closed_count = getattr(env, '_last_closed_count', 0)
        if len(env.closed_positions) > last_closed_count:
            last_position = env.closed_positions[-1]
            pnl_sign = "💰" if last_position.pnl > 0 else "💸"
            print(f"   {pnl_sign} Position closed: P&L ${last_position.pnl:.2f} ({last_position.pnl_pct:.2%})")
            env._last_closed_count = len(env.closed_positions)

        if not hasattr(env, '_last_closed_count'):
            env._last_closed_count = 0

    # Step 4: Generate performance report
    print("\n📊 Step 4: Performance Analysis")
    print("=" * 30)

    metrics = env.get_metrics()

    print(f"Final Balance:     ${metrics['balance']:.2f}")
    print(f"Total Return:      {metrics['return_pct']:.2%}")
    print(f"Max Drawdown:      {metrics['max_drawdown']:.2%}")
    print(f"Total Trades:      {metrics['trades']}")
    print(f"Win Rate:          {metrics['win_rate']:.1f}%")
    print(f"Sharpe Ratio:      {metrics['sharpe_ratio']:.2f}")

    # Step 5: Validate system integrity
    print("\n✅ Step 5: System Validation")
    print("=" * 25)

    validations = []

    # Check balance is positive
    validations.append(("Balance > 0", metrics['balance'] > 0))

    # Check no open positions (all should be closed or expired)
    validations.append(("No open positions", len(env.positions) == 0))

    # Check metrics are reasonable
    validations.append(("Return is finite", abs(metrics['return_pct']) < 1.0))  # Less than 100%
    validations.append(("Drawdown is reasonable", metrics['max_drawdown'] <= 0.5))  # Less than 50%

    # Check data integrity
    validations.append(("Market data valid", len(market_data) >= 20))
    validations.append(("Price data consistent", all(c.high >= c.low for c in market_data)))

    all_passed = True
    for description, passed in validations:
        status = "✅" if passed else "❌"
        print(f"{status} {description}")
        if not passed:
            all_passed = False

    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 COMPLETE SYSTEM TEST PASSED!")
        print("   All components working correctly with real market data.")
    else:
        print("❌ SYSTEM TEST FAILED!")
        print("   Some validations failed - check implementation.")

    return all_passed


if __name__ == "__main__":
    print("Testing Complete Trading System with Real Market Data")
    print("This test validates the entire pipeline from data to execution.")
    print()

    success = asyncio.run(test_complete_trading_system())

    if success:
        print("\n🚀 Ready for next phase of implementation!")
    else:
        print("\n🔧 System needs fixes before proceeding.")
