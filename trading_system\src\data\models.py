"""
Data models for the trading system.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator, root_validator, PositiveFloat

class OHLCVSchema(BaseModel):
    """OHLCV data model with validation."""
    timestamp: datetime
    open: PositiveFloat
    high: PositiveFloat
    low: PositiveFloat
    close: PositiveFloat
    volume: float = Field(..., ge=0)
    symbol: str
    interval: str

    @validator('high')
    def high_must_be_highest(cls, v, values):
        if 'open' in values and 'low' in values and 'close' in values:
            prices = [values['open'], values['close'], values['low']]
            if v < max(prices):
                raise ValueError('High must be >= open, low, and close')
        return v

    @validator('low')
    def low_must_be_lowest(cls, v, values):
        if 'open' in values and 'high' in values and 'close' in values:
            prices = [values['open'], values['close'], values['high']]
            if v > min(prices):
                raise ValueError('Low must be <= open, high, and close')
        return v

class DataFetchRequest(BaseModel):
    """Request model for fetching OHLCV data."""
    symbol: str
    interval: str = '1h'
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    limit: int = 1000

class DataFetchResponse(BaseModel):
    """Response model for data fetch operations."""
    success: bool
    data: Optional[list[OHLCVSchema]] = None
    error: Optional[str] = None
    metadata: dict = Field(default_factory=dict)
