# 🚀 VPS DEPLOYMENT GUIDE - LIVE TRADING SYSTEM

## 📋 OVERVIEW
Deploy the TCN-CNN-PPO trading system to Ubuntu VPS for live trading execution.

**VPS Details:**
- IP: ***********
- OS: Ubuntu 24.04
- Purpose: Live trading execution only
- Development: Remains on laptop

## 🎯 DEPLOYMENT STRATEGY

### Phase 1: VPS Setup & Environment
### Phase 2: Code Deployment
### Phase 3: Model Transfer
### Phase 4: Live Trading Configuration
### Phase 5: Monitoring & Maintenance

---

## 📦 PHASE 1: VPS SETUP & ENVIRONMENT

### Step 1: Connect to VPS
```bash
# Connect via SSH
ssh root@***********

# Or if you have a username
ssh username@***********
```

### Step 2: System Update
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git vim htop screen tmux
```

### Step 3: Python Environment Setup
```bash
# Install Python 3.11+ and pip
sudo apt install -y python3.11 python3.11-pip python3.11-venv

# Create trading user (recommended for security)
sudo useradd -m -s /bin/bash trading
sudo usermod -aG sudo trading

# Switch to trading user
sudo su - trading

# Create project directory
mkdir -p /home/<USER>/live_trading
cd /home/<USER>/live_trading
```

### Step 4: Python Virtual Environment
```bash
# Create virtual environment
python3.11 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Upgrade pip
pip install --upgrade pip
```

### Step 5: Install Dependencies
```bash
# Install core dependencies
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install numpy pandas asyncio aiohttp python-binance
pip install schedule python-dotenv logging

# Install additional packages
pip install psutil requests websockets
```

---

## 📦 PHASE 2: CODE DEPLOYMENT

### Step 1: Create Project Structure
```bash
# Create directory structure
mkdir -p /home/<USER>/live_trading/{src,models,logs,config,data}
mkdir -p /home/<USER>/live_trading/src/{ml,trading,utils}
```

### Step 2: Transfer Core Files
**From your laptop, transfer these files:**

1. **Core Trading Engine**
   - `src/trading/grid_trading_env.py`
   - `src/trading/binance_data_fetcher.py`
   - `src/utils/trading_feature_extractor.py`

2. **ML Model Files**
   - `src/ml/integrated_training.py` (SimpleTCNPPO class only)
   - Model weights from best performing cycles

3. **Configuration Files**
   - API keys and settings
   - Trading parameters

### Step 3: File Transfer Commands
```bash
# From your laptop, use SCP to transfer files
scp -r trading_system/src/ trading@***********:/home/<USER>/live_trading/
scp trading_system/models/* trading@***********:/home/<USER>/live_trading/models/
```

---

## 📦 PHASE 3: MODEL TRANSFER

### Step 1: Export Best Model from Laptop
**Run this on your laptop:**

```python
# Save best model for VPS deployment
import torch
from pathlib import Path

# Load your best model (Cycle 73 with 0.8092 composite score)
model_path = "path/to/best_model.pth"
model_state = torch.load(model_path, map_location='cpu')

# Save for VPS deployment
vps_model_path = "vps_deployment/best_model_cycle73.pth"
torch.save(model_state, vps_model_path)
print(f"Model saved for VPS: {vps_model_path}")
```

### Step 2: Transfer Model to VPS
```bash
# Transfer model file
scp vps_deployment/best_model_cycle73.pth trading@***********:/home/<USER>/live_trading/models/
```

---

## 📦 PHASE 4: LIVE TRADING CONFIGURATION

### Step 1: Environment Configuration
```bash
# Create .env file on VPS
cat > /home/<USER>/live_trading/.env << EOF
# Binance API Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=false

# Trading Configuration
INITIAL_CAPITAL=300.0
RISK_PER_TRADE=0.05
GRID_SPACING=0.0025
TAKE_PROFIT_MULTIPLIER=2.0
FEE_RATE=0.001

# Model Configuration
MODEL_PATH=/home/<USER>/live_trading/models/best_model_cycle73.pth
SYMBOL=BTCUSDT
TIMEFRAME=1h

# Logging
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/live_trading/logs/trading.log
EOF
```

### Step 2: Create Live Trading Script
**This will be created in the next step...**

---

## 📦 PHASE 5: MONITORING & MAINTENANCE

### Step 1: Process Management
```bash
# Install supervisor for process management
sudo apt install -y supervisor

# Create supervisor config
sudo tee /etc/supervisor/conf.d/live_trading.conf << EOF
[program:live_trading]
command=/home/<USER>/live_trading/venv/bin/python /home/<USER>/live_trading/live_trading_main.py
directory=/home/<USER>/live_trading
user=trading
autostart=true
autorestart=true
stderr_logfile=/home/<USER>/live_trading/logs/error.log
stdout_logfile=/home/<USER>/live_trading/logs/output.log
environment=PATH="/home/<USER>/live_trading/venv/bin"
EOF

# Reload supervisor
sudo supervisorctl reread
sudo supervisorctl update
```

### Step 2: Monitoring Setup
```bash
# Create monitoring script
cat > /home/<USER>/live_trading/monitor.py << 'EOF'
#!/usr/bin/env python3
import psutil
import time
import logging
from datetime import datetime

logging.basicConfig(
    filename='/home/<USER>/live_trading/logs/monitor.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def monitor_system():
    while True:
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Log metrics
        logging.info(f"CPU: {cpu_percent}% | RAM: {memory.percent}% | Disk: {disk.percent}%")
        
        # Check if trading process is running
        trading_running = any('live_trading_main.py' in p.cmdline() for p in psutil.process_iter(['cmdline']))
        logging.info(f"Trading Process Running: {trading_running}")
        
        time.sleep(300)  # Check every 5 minutes

if __name__ == "__main__":
    monitor_system()
EOF

chmod +x /home/<USER>/live_trading/monitor.py
```

---

## 🔒 SECURITY CONSIDERATIONS

### Step 1: Firewall Setup
```bash
# Configure UFW firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 443  # HTTPS only
sudo ufw deny 80   # Block HTTP
```

### Step 2: SSH Security
```bash
# Disable root login and password authentication
sudo sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
sudo systemctl restart ssh
```

### Step 3: API Key Security
```bash
# Secure .env file
chmod 600 /home/<USER>/live_trading/.env
chown trading:trading /home/<USER>/live_trading/.env
```

---

## 📊 NEXT STEPS

1. **Create Live Trading Engine** - Main execution script
2. **Model Loading System** - Load and run trained models
3. **Real-time Data Pipeline** - Binance WebSocket integration
4. **Position Management** - Order execution and monitoring
5. **Logging & Alerts** - Comprehensive monitoring system

**Continue to the next files for complete implementation...**
