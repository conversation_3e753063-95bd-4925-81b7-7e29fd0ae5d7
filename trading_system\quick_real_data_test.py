"""
Quick Real Data Test
Test the system with real data only - no dummy data
"""
import asyncio
import sys
import json
from datetime import datetime, timedelta, timezone
from pathlib import Path

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.ml.integrated_training import IntegratedTrainingPipeline


async def test_real_data_system():
    """Test the real data system with a quick evaluation."""
    print("🧪 TESTING REAL DATA SYSTEM")
    print("=" * 50)
    print("📊 NO DUMMY DATA - Real Binance market data only")
    print("=" * 50)
    
    # Test data collection
    print("\n📈 Collecting REAL market data from Binance...")
    try:
        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=7)  # 7 days for quick test
            
            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )
            
            if not response.success:
                print(f"❌ Failed to fetch real data: {response.error}")
                return False
            
            all_candles = response.data
            print(f"✅ Collected {len(all_candles)} real candles")
            print(f"📊 Date range: {all_candles[0].timestamp} to {all_candles[-1].timestamp}")
            
            # Split data
            train_size = int(len(all_candles) * 0.7)  # 70% training
            training_data = all_candles[:train_size]
            test_data = all_candles[train_size:]
            
            print(f"📊 Training data: {len(training_data)} candles")
            print(f"📊 Testing data: {len(test_data)} candles")
            
    except Exception as e:
        print(f"❌ Data collection failed: {e}")
        return False
    
    # Test training pipeline
    print(f"\n🚀 Testing training pipeline with REAL data...")
    try:
        pipeline = IntegratedTrainingPipeline(initial_balance=300.0)
        print(f"✅ Pipeline initialized")
        
        # Test episode simulation
        print(f"🎮 Testing episode simulation...")
        episode_data = await pipeline.simulate_episode_detailed(test_data, pipeline.model)
        
        print(f"✅ Episode simulation completed:")
        print(f"   Final balance: ${episode_data['final_balance']:.2f}")
        print(f"   Total return: {episode_data['total_return']:+.2f}%")
        print(f"   Composite score: {episode_data['composite_score']:.4f}")
        print(f"   Number of trades: {len(episode_data.get('detailed_trades', []))}")
        print(f"   Commission paid: ${episode_data.get('commission_paid', 0):.2f}")
        
        # Show sample trades
        detailed_trades = episode_data.get('detailed_trades', [])
        if detailed_trades:
            print(f"\n📋 SAMPLE TRADES (Real Data):")
            for i, trade in enumerate(detailed_trades[:3], 1):
                print(f"   Trade {i}: {trade.get('direction', 'N/A')} | "
                      f"Entry: ${trade.get('entry_price', 0):.2f} | "
                      f"Exit: ${trade.get('exit_price', 0):.2f} | "
                      f"P&L: ${trade.get('pnl_net', 0):+.2f} | "
                      f"Reason: {trade.get('exit_reason', 'N/A')}")
        
        # Test composite metrics
        composite_metrics = episode_data.get('composite_metrics', {})
        if composite_metrics:
            print(f"\n📊 COMPOSITE METRICS (Real Data):")
            for metric, value in composite_metrics.items():
                print(f"   {metric}: {value:.4f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Training pipeline test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_quick_cycles():
    """Run a few quick cycles with real data."""
    print("\n🔄 RUNNING 3 QUICK CYCLES WITH REAL DATA")
    print("-" * 50)
    
    try:
        # Collect real data once
        async with BinanceDataFetcher() as fetcher:
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(days=10)  # 10 days
            
            response = await fetcher.fetch_historical_data(
                symbol="BTCUSDT",
                interval="1h",
                start_time=start_time,
                end_time=end_time
            )
            
            all_candles = response.data
            train_size = int(len(all_candles) * 0.7)
            training_data = all_candles[:train_size]
            test_data = all_candles[train_size:]
        
        best_score = 0
        best_cycle = None
        results = []
        
        for cycle in range(3):
            print(f"\nCycle {cycle + 1}/3:")
            
            # Fresh pipeline for each cycle
            pipeline = IntegratedTrainingPipeline(initial_balance=300.0)
            
            # Quick training (3 episodes)
            for episode in range(3):
                episode_data = await pipeline.simulate_episode_detailed(training_data, pipeline.model)
                if len(episode_data['states']) > 0:
                    await pipeline.quick_training_update(episode_data)
            
            # Test on out-of-sample data
            test_episode = await pipeline.simulate_episode_detailed(test_data, pipeline.model)
            
            score = test_episode['composite_score']
            return_pct = test_episode['total_return']
            trades = len(test_episode.get('detailed_trades', []))
            
            print(f"   Score: {score:.4f} | Return: {return_pct:+.2f}% | Trades: {trades}")
            
            if score > best_score:
                best_score = score
                best_cycle = {
                    'cycle': cycle + 1,
                    'score': score,
                    'return': return_pct,
                    'trades': trades,
                    'detailed_trades': test_episode.get('detailed_trades', [])
                }
            
            results.append({
                'cycle': cycle + 1,
                'score': score,
                'return': return_pct,
                'trades': trades
            })
        
        print(f"\n🏆 BEST CYCLE RESULTS:")
        print(f"   Cycle: {best_cycle['cycle']}")
        print(f"   Score: {best_cycle['score']:.4f}")
        print(f"   Return: {best_cycle['return']:+.2f}%")
        print(f"   Trades: {best_cycle['trades']}")
        
        # Show detailed trades from best cycle
        if best_cycle['detailed_trades']:
            print(f"\n📋 DETAILED TRADES FROM BEST CYCLE (Real Data):")
            for i, trade in enumerate(best_cycle['detailed_trades'][:5], 1):
                print(f"   {i}. {trade.get('direction', 'N/A')} | "
                      f"${trade.get('entry_price', 0):.2f} → ${trade.get('exit_price', 0):.2f} | "
                      f"P&L: ${trade.get('pnl_net', 0):+.2f} | "
                      f"{trade.get('exit_reason', 'N/A')}")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"reports/quick_real_data_test_{timestamp}.json"
        Path(results_file).parent.mkdir(parents=True, exist_ok=True)
        
        with open(results_file, 'w') as f:
            json.dump({
                'results': results,
                'best_cycle': best_cycle,
                'data_source': 'REAL_BINANCE_DATA_ONLY'
            }, f, indent=2, default=str)
        
        print(f"\n📊 Results saved to: {results_file}")
        return True
        
    except Exception as e:
        print(f"❌ Quick cycles failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Main test function."""
    print("🎯 REAL DATA SYSTEM TEST")
    print("=" * 60)
    print("🔴 NO DUMMY DATA POLICY ENFORCED")
    print("✅ Only real Binance market data will be used")
    print("=" * 60)
    
    # Test basic functionality
    basic_test = await test_real_data_system()
    
    if basic_test:
        print(f"\n✅ Basic real data test passed!")
        
        # Run quick cycles
        cycles_test = await run_quick_cycles()
        
        if cycles_test:
            print(f"\n🎉 ALL REAL DATA TESTS PASSED!")
            print(f"✅ System ready for full evaluation")
            print(f"📊 All data sourced from real Binance API")
            print(f"🚫 No dummy data used anywhere")
            
            print(f"\n🚀 READY FOR FULL EVALUATION:")
            print(f"   • 60-day training with real data")
            print(f"   • 30-day out-of-sample testing")
            print(f"   • Best model saving")
            print(f"   • Detailed trade analysis")
            print(f"   • HTML report generation")
        else:
            print(f"\n❌ Cycles test failed")
    else:
        print(f"\n❌ Basic test failed")


if __name__ == "__main__":
    asyncio.run(main())
