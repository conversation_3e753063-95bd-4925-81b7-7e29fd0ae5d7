"""Data loading and preprocessing for cryptocurrency trading."""

import os
import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from pathlib import Path
import ccxt
import joblib
from tqdm import tqdm

from .dataset import CryptoDataset
from .transforms import Compose, AddIndicators, Normalize, ToTensor, RandomTimeShift, RandomNoise
from ..utils.logger import setup_logger

logger = setup_logger(__name__)

class CryptoDataLoader:
    """Handles loading and preprocessing of cryptocurrency market data."""
    
    def __init__(self, config):
        """
        Initialize the data loader.
        
        Args:
            config: Configuration object with data loading parameters
        """
        self.config = config
        self.data_dir = Path(config.data_dir)
        self.cache_dir = self.data_dir / "cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize CCXT exchange
        self.exchange = ccxt.binance({
            'enableRateLimit': True,
            'options': {
                'defaultType': 'future',
                'adjustForTimeDifference': True,
            }
        })
        
        # Load or download data
        self.data = self._load_or_download_data()
        
        # Prepare datasets
        self.train_data, self.val_data, self.test_data = self._prepare_datasets()
    
    def _load_or_download_data(self) -> Dict[str, pd.DataFrame]:
        """Load data from cache or download it if not available."""
        data = {}
        
        for symbol in self.config.symbols:
            for tf in self.config.timeframes:
                cache_file = self.cache_dir / f"{symbol.replace('/', '-')}_{tf}.pkl"
                
                if cache_file.exists():
                    # Load from cache
                    df = pd.read_pickle(cache_file)
                    logger.info(f"Loaded {symbol} {tf} data from cache: {len(df)} rows")
                else:
                    # Download data
                    logger.info(f"Downloading {symbol} {tf} data from Binance...")
                    df = self._fetch_ohlcv(symbol, tf)
                    df.to_pickle(cache_file)
                    logger.info(f"Saved {symbol} {tf} data to {cache_file}")
                
                data[f"{symbol}_{tf}"] = df
        
        return data
    
    def _fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 1000) -> pd.DataFrame:
        """Fetch OHLCV data from exchange."""
        since = self.exchange.parse8601('2017-01-01T00:00:00Z')
        all_ohlcv = []
        
        with tqdm(desc=f"Downloading {symbol} {timeframe}") as pbar:
            while True:
                try:
                    ohlcv = self.exchange.fetch_ohlcv(
                        symbol, 
                        timeframe, 
                        since=since,
                        limit=limit
                    )
                    
                    if not ohlcv:
                        break
                        
                    all_ohlcv.extend(ohlcv)
                    since = ohlcv[-1][0] + 1  # Next candle
                    pbar.update(len(ohlcv))
                    
                    # Sleep to avoid rate limits
                    time.sleep(self.exchange.rateLimit / 1000)
                    
                except Exception as e:
                    logger.error(f"Error fetching data: {e}")
                    break
        
        # Convert to DataFrame
        df = pd.DataFrame(
            all_ohlcv, 
            columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
        )
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        # Remove duplicates
        df = df[~df.index.duplicated(keep='first')]
        
        return df
    
    def _prepare_datasets(self) -> Tuple[CryptoDataset, CryptoDataset, CryptoDataset]:
        """Prepare train/validation/test datasets."""
        # Combine data from all symbols and timeframes
        combined_data = []
        
        for (symbol_tf, df) in self.data.items():
            # Add technical indicators
            transforms = [
                AddIndicators(indicators=self.config.indicators),
                Normalize(method=self.config.scale_method)
            ]
            
            # Add data augmentation for training
            if self.config.use_augmentation:
                transforms.extend([
                    RandomTimeShift(prob=self.config.aug_prob),
                    RandomNoise(prob=self.config.aug_prob)
                ])
            
            transforms.append(ToTensor())
            
            # Create dataset
            dataset = CryptoDataset(
                df=df,
                seq_len=self.config.seq_len,
                transform=Compose(transforms)
            )
            
            combined_data.append(dataset)
        
        # Combine datasets from different symbols/timeframes
        if len(combined_data) > 1:
            dataset = torch.utils.data.ConcatDataset(combined_data)
        else:
            dataset = combined_data[0]
        
        # Split into train/val/test
        train_size = int(len(dataset) * self.config.train_ratio)
        val_size = int(len(dataset) * self.config.val_ratio)
        test_size = len(dataset) - train_size - val_size
        
        train_dataset, val_dataset, test_dataset = torch.utils.data.random_split(
            dataset, 
            [train_size, val_size, test_size],
            generator=torch.Generator().manual_seed(self.config.training.seed)
        )
        
        # Save dataset statistics for normalization
        self._save_dataset_stats(train_dataset)
        
        return train_dataset, val_dataset, test_dataset
    
    def _save_dataset_stats(self, dataset) -> None:
        """Calculate and save dataset statistics for normalization."""
        # This would be implemented to calculate mean/std for each feature
        # For now, we'll just save a placeholder
        stats = {
            'feature_means': np.zeros(dataset[0][0].shape[1]),
            'feature_stds': np.ones(dataset[0][0].shape[1])
        }
        
        stats_file = self.cache_dir / 'dataset_stats.pkl'
        joblib.dump(stats, stats_file)
        logger.info(f"Saved dataset statistics to {stats_file}")
    
    def get_data_loaders(self, batch_size: int = None) -> Dict[str, torch.utils.data.DataLoader]:
        """Get data loaders for train/val/test splits."""
        batch_size = batch_size or self.config.batch_size
        
        loaders = {
            'train': torch.utils.data.DataLoader(
                self.train_data,
                batch_size=batch_size,
                shuffle=True,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory,
                drop_last=True
            ),
            'val': torch.utils.data.DataLoader(
                self.val_data,
                batch_size=batch_size * 2,  # Larger batch for validation
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory
            ),
            'test': torch.utils.data.DataLoader(
                self.test_data,
                batch_size=batch_size * 2,  # Larger batch for testing
                shuffle=False,
                num_workers=self.config.num_workers,
                pin_memory=self.config.pin_memory
            )
        }
        
        return loaders
