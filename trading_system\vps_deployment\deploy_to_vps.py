#!/usr/bin/env python3
"""
VPS Deployment Script
Automate the deployment of trading system to Ubuntu VPS
"""
import os
import sys
import subprocess
import json
import torch
from pathlib import Path
from datetime import datetime
from typing import Dict, List


class VPSDeployer:
    """Automated VPS deployment system."""
    
    def __init__(self, vps_ip: str = "***********", vps_user: str = "trading"):
        self.vps_ip = vps_ip
        self.vps_user = vps_user
        self.vps_path = f"/home/<USER>/live_trading"
        self.local_path = Path(__file__).parent
        
        print(f"🚀 VPS Deployer initialized")
        print(f"   VPS: {vps_user}@{vps_ip}")
        print(f"   Remote path: {self.vps_path}")
    
    def export_best_model(self) -> str:
        """Export the best trained model for VPS deployment."""
        print("\n📦 EXPORTING BEST MODEL FOR VPS...")
        
        try:
            # For this example, we'll create a dummy model
            # In practice, you'd load your actual best model
            from live_trading_main import SimpleTCNPPO
            
            # Create model instance
            model = SimpleTCNPPO(input_size=216, hidden_size=128, num_actions=3)
            
            # Save model for VPS
            model_path = self.local_path / "best_model_cycle73.pth"
            torch.save(model.state_dict(), model_path)
            
            print(f"✅ Model exported: {model_path}")
            return str(model_path)
            
        except Exception as e:
            print(f"❌ Model export failed: {e}")
            raise
    
    def create_deployment_package(self) -> Dict[str, List[str]]:
        """Create deployment package with all necessary files."""
        print("\n📦 CREATING DEPLOYMENT PACKAGE...")
        
        # Define files to deploy
        deployment_files = {
            'core': [
                'live_trading_main.py',
                'requirements.txt',
                '.env.template'
            ],
            'src': [
                '../src/trading/grid_trading_env.py',
                '../src/trading/binance_data_fetcher.py',
                '../src/utils/trading_feature_extractor.py'
            ],
            'models': [
                'best_model_cycle73.pth'
            ],
            'config': [
                'supervisor_config.conf',
                'systemd_service.service'
            ]
        }
        
        # Create requirements.txt
        self.create_requirements_file()
        
        # Create environment template
        self.create_env_template()
        
        # Create supervisor config
        self.create_supervisor_config()
        
        # Create systemd service
        self.create_systemd_service()
        
        print("✅ Deployment package created")
        return deployment_files
    
    def create_requirements_file(self):
        """Create requirements.txt for VPS."""
        requirements = [
            "torch>=2.0.0",
            "numpy>=1.24.0",
            "pandas>=2.0.0",
            "aiohttp>=3.8.0",
            "python-binance>=1.0.0",
            "python-dotenv>=1.0.0",
            "schedule>=1.2.0",
            "psutil>=5.9.0",
            "websockets>=11.0.0"
        ]
        
        req_file = self.local_path / "requirements.txt"
        with open(req_file, 'w') as f:
            f.write('\n'.join(requirements))
        
        print(f"✅ Requirements file created: {req_file}")
    
    def create_env_template(self):
        """Create environment template file."""
        env_template = """# Binance API Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=false

# Trading Configuration
INITIAL_CAPITAL=300.0
RISK_PER_TRADE=0.05
GRID_SPACING=0.0025
TAKE_PROFIT_MULTIPLIER=2.0
FEE_RATE=0.001

# Model Configuration
MODEL_PATH=/home/<USER>/live_trading/models/best_model_cycle73.pth
SYMBOL=BTCUSDT
TIMEFRAME=1h

# Logging
LOG_LEVEL=INFO
LOG_FILE=/home/<USER>/live_trading/logs/trading.log

# Monitoring
ENABLE_MONITORING=true
ALERT_EMAIL=<EMAIL>
"""
        
        env_file = self.local_path / ".env.template"
        with open(env_file, 'w') as f:
            f.write(env_template)
        
        print(f"✅ Environment template created: {env_file}")
    
    def create_supervisor_config(self):
        """Create supervisor configuration."""
        supervisor_config = f"""[program:live_trading]
command={self.vps_path}/venv/bin/python {self.vps_path}/live_trading_main.py
directory={self.vps_path}
user={self.vps_user}
autostart=true
autorestart=true
stderr_logfile={self.vps_path}/logs/error.log
stdout_logfile={self.vps_path}/logs/output.log
environment=PATH="{self.vps_path}/venv/bin"
redirect_stderr=true
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
"""
        
        config_file = self.local_path / "supervisor_config.conf"
        with open(config_file, 'w') as f:
            f.write(supervisor_config)
        
        print(f"✅ Supervisor config created: {config_file}")
    
    def create_systemd_service(self):
        """Create systemd service file."""
        service_config = f"""[Unit]
Description=Live Trading System
After=network.target

[Service]
Type=simple
User={self.vps_user}
WorkingDirectory={self.vps_path}
Environment=PATH={self.vps_path}/venv/bin
ExecStart={self.vps_path}/venv/bin/python {self.vps_path}/live_trading_main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
"""
        
        service_file = self.local_path / "systemd_service.service"
        with open(service_file, 'w') as f:
            f.write(service_config)
        
        print(f"✅ Systemd service created: {service_file}")
    
    def run_ssh_command(self, command: str) -> bool:
        """Run command on VPS via SSH."""
        ssh_command = f"ssh {self.vps_user}@{self.vps_ip} '{command}'"
        
        try:
            result = subprocess.run(ssh_command, shell=True, capture_output=True, text=True)
            if result.returncode == 0:
                return True
            else:
                print(f"❌ SSH command failed: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ SSH error: {e}")
            return False
    
    def transfer_files(self, files: Dict[str, List[str]]) -> bool:
        """Transfer files to VPS using SCP."""
        print("\n📤 TRANSFERRING FILES TO VPS...")
        
        try:
            # Create directories on VPS
            directories = ['src/trading', 'src/utils', 'models', 'logs', 'config']
            for directory in directories:
                command = f"mkdir -p {self.vps_path}/{directory}"
                self.run_ssh_command(command)
            
            # Transfer files
            for category, file_list in files.items():
                print(f"   Transferring {category} files...")
                
                for file_path in file_list:
                    local_file = self.local_path / file_path
                    
                    if local_file.exists():
                        # Determine remote path
                        if category == 'src':
                            remote_path = f"{self.vps_path}/src/"
                        elif category == 'models':
                            remote_path = f"{self.vps_path}/models/"
                        elif category == 'config':
                            remote_path = f"{self.vps_path}/config/"
                        else:
                            remote_path = f"{self.vps_path}/"
                        
                        # Transfer file
                        scp_command = f"scp {local_file} {self.vps_user}@{self.vps_ip}:{remote_path}"
                        result = subprocess.run(scp_command, shell=True, capture_output=True)
                        
                        if result.returncode == 0:
                            print(f"     ✅ {file_path}")
                        else:
                            print(f"     ❌ {file_path} - {result.stderr.decode()}")
                    else:
                        print(f"     ⚠️  {file_path} - File not found")
            
            print("✅ File transfer completed")
            return True
            
        except Exception as e:
            print(f"❌ File transfer failed: {e}")
            return False
    
    def setup_vps_environment(self) -> bool:
        """Setup Python environment on VPS."""
        print("\n🔧 SETTING UP VPS ENVIRONMENT...")
        
        commands = [
            # Update system
            "sudo apt update && sudo apt upgrade -y",
            
            # Install Python and dependencies
            "sudo apt install -y python3.11 python3.11-pip python3.11-venv git htop screen tmux",
            
            # Create virtual environment
            f"cd {self.vps_path} && python3.11 -m venv venv",
            
            # Activate venv and install packages
            f"cd {self.vps_path} && source venv/bin/activate && pip install --upgrade pip",
            f"cd {self.vps_path} && source venv/bin/activate && pip install -r requirements.txt",
            
            # Set permissions
            f"chmod +x {self.vps_path}/live_trading_main.py",
            f"chmod 600 {self.vps_path}/.env",
            
            # Create log directory
            f"mkdir -p {self.vps_path}/logs"
        ]
        
        for command in commands:
            print(f"   Running: {command[:50]}...")
            if not self.run_ssh_command(command):
                print(f"❌ Failed to execute: {command}")
                return False
        
        print("✅ VPS environment setup completed")
        return True
    
    def deploy(self) -> bool:
        """Execute full deployment process."""
        print("🚀 STARTING VPS DEPLOYMENT PROCESS")
        print("=" * 50)
        
        try:
            # Step 1: Export model
            self.export_best_model()
            
            # Step 2: Create deployment package
            files = self.create_deployment_package()
            
            # Step 3: Transfer files
            if not self.transfer_files(files):
                return False
            
            # Step 4: Setup environment
            if not self.setup_vps_environment():
                return False
            
            # Step 5: Final configuration
            print("\n⚙️  FINAL CONFIGURATION...")
            
            # Copy environment template
            env_command = f"cp {self.vps_path}/.env.template {self.vps_path}/.env"
            self.run_ssh_command(env_command)
            
            print("\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!")
            print("\n📋 NEXT STEPS:")
            print(f"1. SSH to VPS: ssh {self.vps_user}@{self.vps_ip}")
            print(f"2. Edit .env file: nano {self.vps_path}/.env")
            print("3. Add your Binance API keys")
            print(f"4. Test the system: cd {self.vps_path} && python live_trading_main.py")
            print("5. Setup supervisor for production")
            
            return True
            
        except Exception as e:
            print(f"❌ Deployment failed: {e}")
            return False


def main():
    """Main deployment function."""
    print("🚀 VPS DEPLOYMENT TOOL")
    print("=" * 30)
    
    # Initialize deployer
    deployer = VPSDeployer()
    
    # Run deployment
    success = deployer.deploy()
    
    if success:
        print("\n✅ Deployment completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Deployment failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
