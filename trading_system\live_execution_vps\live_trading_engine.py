#!/usr/bin/env python3
"""
Live Trading Engine for VPS Deployment
TCN-CNN-PPO Algorithm Live Trading System
Optimized for Ubuntu VPS with 24/7 operation
"""
import asyncio
import logging
import os
import sys
import signal
import json
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import torch
import torch.nn as nn
import numpy as np
from dataclasses import dataclass


@dataclass
class TradingConfig:
    """Live trading configuration."""
    # Model settings
    model_path: str = "models/live_model.pth"
    input_size: int = 216
    hidden_size: int = 128
    num_actions: int = 3
    
    # Trading settings
    initial_capital: float = 300.0
    risk_per_trade: float = 0.05  # 5% risk
    grid_spacing: float = 0.0025  # 0.25%
    take_profit_multiplier: float = 2.0  # 2:1 ratio
    fee_rate: float = 0.001  # 0.1% commission
    
    # Market settings
    symbol: str = "BTCUSDT"
    timeframe: str = "1h"
    lookback_periods: int = 24
    
    # Risk management
    max_positions: int = 5
    stop_loss_pct: float = 0.05
    max_daily_trades: int = 20
    max_daily_loss_pct: float = 0.10  # 10% max daily loss
    
    # Operational settings
    decision_interval: int = 3600  # 1 hour in seconds
    health_check_interval: int = 300  # 5 minutes
    log_level: str = "INFO"


class SimpleTCNPPO(nn.Module):
    """Simplified TCN-PPO model for live trading."""
    
    def __init__(self, input_size: int = 216, hidden_size: int = 128, num_actions: int = 3):
        super().__init__()
        self.input_size = input_size
        self.hidden_size = hidden_size
        self.num_actions = num_actions
        
        # Feature processing
        self.feature_net = nn.Sequential(
            nn.Linear(input_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_size, hidden_size),
            nn.ReLU(),
            nn.Dropout(0.2)
        )
        
        # Policy head (action probabilities)
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, num_actions),
            nn.Softmax(dim=-1)
        )
        
        # Value head (state value)
        self.value_head = nn.Sequential(
            nn.Linear(hidden_size, hidden_size // 2),
            nn.ReLU(),
            nn.Linear(hidden_size // 2, 1)
        )
    
    def forward(self, x):
        features = self.feature_net(x)
        policy = self.policy_head(features)
        value = self.value_head(features)
        return policy, value


class BinanceDataFetcher:
    """Binance API data fetcher."""
    
    def __init__(self):
        self.logger = logging.getLogger('BinanceDataFetcher')
        self.base_url = "https://api.binance.com"
    
    async def fetch_klines(self, symbol: str, interval: str, limit: int = 100) -> List[Dict]:
        """Fetch kline data from Binance."""
        import aiohttp
        
        url = f"{self.base_url}/api/v3/klines"
        params = {
            'symbol': symbol,
            'interval': interval,
            'limit': limit
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        processed_data = []
                        for kline in data:
                            processed_data.append({
                                'timestamp': int(kline[0]),
                                'open': float(kline[1]),
                                'high': float(kline[2]),
                                'low': float(kline[3]),
                                'close': float(kline[4]),
                                'volume': float(kline[5]),
                                'close_time': int(kline[6]),
                                'quote_volume': float(kline[7]),
                                'trades': int(kline[8]),
                                'taker_buy_base': float(kline[9]),
                                'taker_buy_quote': float(kline[10])
                            })
                        
                        return processed_data
                    else:
                        raise Exception(f"API request failed: {response.status}")
                        
        except Exception as e:
            self.logger.error(f"Error fetching klines: {e}")
            raise


class FeatureExtractor:
    """Extract trading features from market data."""
    
    def __init__(self, lookback_window: int = 24):
        self.lookback_window = lookback_window
        self.logger = logging.getLogger('FeatureExtractor')
    
    def extract_features(self, candles: List[Dict]) -> np.ndarray:
        """Extract features from candle data."""
        if len(candles) < self.lookback_window:
            raise ValueError(f"Need at least {self.lookback_window} candles, got {len(candles)}")
        
        # Use last lookback_window candles
        recent_candles = candles[-self.lookback_window:]
        
        features = []
        
        for candle in recent_candles:
            # Basic OHLCV features
            features.extend([
                candle['open'],
                candle['high'],
                candle['low'],
                candle['close'],
                candle['volume'],
                candle['quote_volume'],
                candle['trades'],
                candle['taker_buy_base'],
                candle['taker_buy_quote']
            ])
        
        # Convert to numpy array and normalize
        features_array = np.array(features, dtype=np.float32)
        
        # Simple normalization
        features_array = (features_array - np.mean(features_array)) / (np.std(features_array) + 1e-8)
        
        return features_array


class RiskManager:
    """Risk management system."""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.daily_trades = 0
        self.daily_pnl = 0.0
        self.last_reset_date = datetime.now(timezone.utc).date()
        self.logger = logging.getLogger('RiskManager')
    
    def reset_daily_counters(self):
        """Reset daily counters if new day."""
        current_date = datetime.now(timezone.utc).date()
        if current_date != self.last_reset_date:
            self.daily_trades = 0
            self.daily_pnl = 0.0
            self.last_reset_date = current_date
            self.logger.info("Daily counters reset")
    
    def can_trade(self, current_balance: float) -> Tuple[bool, str]:
        """Check if trading is allowed."""
        self.reset_daily_counters()
        
        # Check daily trade limit
        if self.daily_trades >= self.config.max_daily_trades:
            return False, f"Daily trade limit reached ({self.daily_trades}/{self.config.max_daily_trades})"
        
        # Check daily loss limit
        daily_loss_pct = abs(self.daily_pnl) / self.config.initial_capital
        if self.daily_pnl < 0 and daily_loss_pct >= self.config.max_daily_loss_pct:
            return False, f"Daily loss limit reached ({daily_loss_pct:.2%})"
        
        # Check minimum balance
        if current_balance < self.config.initial_capital * 0.5:  # 50% of initial capital
            return False, f"Balance too low (${current_balance:.2f})"
        
        return True, "OK"
    
    def calculate_position_size(self, current_balance: float, current_price: float) -> float:
        """Calculate position size based on risk management."""
        risk_amount = current_balance * self.config.risk_per_trade
        position_size = risk_amount / current_price
        return position_size
    
    def record_trade(self, pnl: float):
        """Record trade for daily tracking."""
        self.daily_trades += 1
        self.daily_pnl += pnl
        self.logger.info(f"Trade recorded: PnL=${pnl:.2f}, Daily: {self.daily_trades} trades, ${self.daily_pnl:.2f} PnL")


class LiveTradingEngine:
    """Main live trading engine."""
    
    def __init__(self, config: TradingConfig):
        self.config = config
        self.running = False
        
        # Components
        self.model = None
        self.data_fetcher = None
        self.feature_extractor = None
        self.risk_manager = None
        
        # Trading state
        self.current_balance = config.initial_capital
        self.positions = []
        self.trade_history = []
        self.last_decision_time = None
        
        # Setup logging
        self.setup_logging()
        
        # Load environment variables
        self.load_environment()
        
        self.logger.info("Live Trading Engine initialized")
    
    def setup_logging(self):
        """Setup comprehensive logging."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'live_trading.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger('LiveTradingEngine')
        self.logger.info("Logging system initialized")
    
    def load_environment(self):
        """Load environment variables."""
        # Load from .env file if available
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.startswith('#'):
                        key, value = line.strip().split('=', 1)
                        os.environ[key] = value
        
        self.logger.info("Environment variables loaded")
    
    async def initialize(self):
        """Initialize all components."""
        try:
            self.logger.info("Initializing live trading components...")
            
            # Load model
            await self.load_model()
            
            # Initialize components
            self.data_fetcher = BinanceDataFetcher()
            self.feature_extractor = FeatureExtractor(lookback_window=self.config.lookback_periods)
            self.risk_manager = RiskManager(self.config)
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Initialization failed: {e}")
            raise
    
    async def load_model(self):
        """Load the trained model."""
        try:
            model_path = Path(self.config.model_path)
            if not model_path.exists():
                raise FileNotFoundError(f"Model file not found: {model_path}")
            
            # Initialize model
            self.model = SimpleTCNPPO(
                input_size=self.config.input_size,
                hidden_size=self.config.hidden_size,
                num_actions=self.config.num_actions
            )
            
            # Load weights
            checkpoint = torch.load(model_path, map_location='cpu')
            
            # Handle different checkpoint formats
            if isinstance(checkpoint, dict) and 'model_state_dict' in checkpoint:
                self.model.load_state_dict(checkpoint['model_state_dict'])
            else:
                self.model.load_state_dict(checkpoint)
            
            # Set to evaluation mode
            self.model.eval()
            
            self.logger.info(f"Model loaded successfully from {model_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to load model: {e}")
            raise
    
    async def get_market_data(self) -> List[Dict]:
        """Fetch latest market data."""
        try:
            candles = await self.data_fetcher.fetch_klines(
                symbol=self.config.symbol,
                interval=self.config.timeframe,
                limit=self.config.lookback_periods + 10  # Extra buffer
            )
            
            if len(candles) < self.config.lookback_periods:
                raise RuntimeError(f"Insufficient market data: {len(candles)} candles")
            
            return candles
            
        except Exception as e:
            self.logger.error(f"Failed to get market data: {e}")
            raise
    
    async def make_trading_decision(self, candles: List[Dict]) -> int:
        """Make trading decision using the model."""
        try:
            # Extract features
            features = self.feature_extractor.extract_features(candles)
            
            # Convert to tensor
            feature_tensor = torch.FloatTensor(features).unsqueeze(0)
            
            # Get model prediction
            with torch.no_grad():
                policy, value = self.model(feature_tensor)
                action_probs = policy.squeeze(0)
                
                # Get action with highest probability
                action = torch.argmax(action_probs).item()
            
            # Log decision
            self.logger.info(f"Model decision: Action={action}, Probs={action_probs.tolist()}, Value={value.item():.3f}")
            
            return action
            
        except Exception as e:
            self.logger.error(f"Failed to make trading decision: {e}")
            return 2  # Default to HOLD
    
    async def execute_trade(self, action: int, current_price: float):
        """Execute trading action."""
        try:
            action_names = {0: "BUY", 1: "SELL", 2: "HOLD"}
            action_name = action_names.get(action, "UNKNOWN")
            
            # Check if trading is allowed
            can_trade, reason = self.risk_manager.can_trade(self.current_balance)
            if not can_trade:
                self.logger.warning(f"Trading blocked: {reason}")
                return
            
            self.logger.info(f"Executing action: {action_name} at price ${current_price:.2f}")
            
            if action == 0:  # BUY
                await self.execute_buy_order(current_price)
            elif action == 1:  # SELL
                await self.execute_sell_order(current_price)
            else:  # HOLD
                self.logger.info("Holding position - no action taken")
            
            # Update last decision time
            self.last_decision_time = datetime.now(timezone.utc)
            
        except Exception as e:
            self.logger.error(f"Failed to execute trade: {e}")
    
    async def execute_buy_order(self, price: float):
        """Execute buy order (simulated for now)."""
        # Calculate position size
        position_size = self.risk_manager.calculate_position_size(self.current_balance, price)
        
        # Create trade record
        trade = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'action': 'BUY',
            'price': price,
            'size': position_size,
            'cost': price * position_size,
            'fee': price * position_size * self.config.fee_rate,
            'balance_before': self.current_balance
        }
        
        # Update balance (subtract cost + fees)
        total_cost = trade['cost'] + trade['fee']
        self.current_balance -= total_cost
        
        # Add to positions
        self.positions.append({
            'type': 'LONG',
            'entry_price': price,
            'size': position_size,
            'timestamp': trade['timestamp']
        })
        
        # Record trade
        self.trade_history.append(trade)
        self.risk_manager.record_trade(-total_cost)
        
        self.logger.info(f"BUY ORDER EXECUTED: Size={position_size:.6f}, Cost=${total_cost:.2f}")
        self.logger.info(f"New balance: ${self.current_balance:.2f}")
    
    async def execute_sell_order(self, price: float):
        """Execute sell order (simulated for now)."""
        if not self.positions:
            self.logger.info("No positions to sell")
            return
        
        # Close oldest position
        position = self.positions.pop(0)
        
        # Calculate P&L
        revenue = price * position['size']
        cost = position['entry_price'] * position['size']
        fee = revenue * self.config.fee_rate
        pnl = revenue - cost - fee
        
        # Create trade record
        trade = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'action': 'SELL',
            'price': price,
            'size': position['size'],
            'revenue': revenue,
            'fee': fee,
            'pnl': pnl,
            'return_pct': (pnl / cost) * 100,
            'balance_before': self.current_balance
        }
        
        # Update balance
        self.current_balance += revenue - fee
        
        # Record trade
        self.trade_history.append(trade)
        self.risk_manager.record_trade(pnl)
        
        self.logger.info(f"SELL ORDER EXECUTED: PnL=${pnl:.2f}, Return={trade['return_pct']:.2f}%")
        self.logger.info(f"New balance: ${self.current_balance:.2f}")
    
    async def run_trading_loop(self):
        """Main trading loop."""
        self.logger.info("Starting live trading loop...")
        self.running = True
        
        while self.running:
            try:
                # Get market data
                candles = await self.get_market_data()
                current_price = candles[-1]['close']
                
                # Make trading decision
                action = await self.make_trading_decision(candles)
                
                # Execute trade
                await self.execute_trade(action, current_price)
                
                # Log status
                self.log_status(current_price)
                
                # Wait for next decision interval
                await asyncio.sleep(self.config.decision_interval)
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute before retry
    
    def log_status(self, current_price: float):
        """Log current trading status."""
        status = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'current_price': current_price,
            'balance': self.current_balance,
            'positions': len(self.positions),
            'total_trades': len(self.trade_history),
            'daily_trades': self.risk_manager.daily_trades,
            'daily_pnl': self.risk_manager.daily_pnl,
            'total_return_pct': ((self.current_balance - self.config.initial_capital) / self.config.initial_capital) * 100
        }
        
        self.logger.info(f"STATUS: {status}")
    
    def shutdown(self):
        """Graceful shutdown."""
        self.logger.info("Shutting down live trading engine...")
        self.running = False
        
        # Save final state
        final_state = {
            'shutdown_time': datetime.now(timezone.utc).isoformat(),
            'final_balance': self.current_balance,
            'total_trades': len(self.trade_history),
            'positions': self.positions,
            'recent_trades': self.trade_history[-10:] if self.trade_history else []
        }
        
        # Save to file
        with open('logs/final_state.json', 'w') as f:
            json.dump(final_state, f, indent=2)
        
        self.logger.info("Shutdown complete")


async def main():
    """Main entry point."""
    # Load configuration
    config = TradingConfig()
    
    # Override with environment variables if available
    if os.getenv('MODEL_PATH'):
        config.model_path = os.getenv('MODEL_PATH')
    if os.getenv('INITIAL_CAPITAL'):
        config.initial_capital = float(os.getenv('INITIAL_CAPITAL'))
    if os.getenv('SYMBOL'):
        config.symbol = os.getenv('SYMBOL')
    
    # Initialize engine
    engine = LiveTradingEngine(config)
    
    # Setup signal handlers
    def signal_handler(signum, frame):
        print(f"\nReceived signal {signum}, shutting down...")
        engine.shutdown()
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        # Initialize components
        await engine.initialize()
        
        # Start trading
        await engine.run_trading_loop()
        
    except KeyboardInterrupt:
        print("\nKeyboard interrupt received")
    except Exception as e:
        print(f"Fatal error: {e}")
    finally:
        engine.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
