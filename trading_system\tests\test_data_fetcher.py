"""
Integration tests for the Binance data fetcher using real API data.
"""
import asyncio
import sys
from datetime import datetime, timedelta, timezone
import pytest
import pytest_asyncio

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from src.data.binance_fetcher import BinanceDataFetcher
from src.data.models import OHLCVSchema

@pytest_asyncio.fixture
async def binance_fetcher():
    """Create a BinanceDataFetcher instance for testing with real API."""
    async with BinanceDataFetcher() as fetcher:
        yield fetcher

@pytest.mark.asyncio
async def test_fetch_real_klines_success(binance_fetcher):
    """Test successful klines fetch with real Binance API data."""
    # Fetch real BTC/USDT 1h data (last 5 candles)
    response = await binance_fetcher.fetch_klines("BTCUSDT", "1h", limit=5)

    # Verify response structure
    assert response.success is True
    assert response.data is not None
    assert len(response.data) == 5
    assert isinstance(response.data[0], OHLCVSchema)

    # Verify data integrity
    for candle in response.data:
        assert candle.symbol == "BTCUSDT"
        assert candle.interval == "1h"
        assert candle.open > 0
        assert candle.high >= candle.open
        assert candle.high >= candle.close
        assert candle.high >= candle.low
        assert candle.low <= candle.open
        assert candle.low <= candle.close
        assert candle.volume >= 0
        assert isinstance(candle.timestamp, datetime)

@pytest.mark.asyncio
async def test_fetch_real_historical_data(binance_fetcher):
    """Test historical data fetching with real Binance API data."""
    # Fetch real historical data for the last 24 hours
    end_time = datetime.now(timezone.utc)
    start_time = end_time - timedelta(hours=24)

    response = await binance_fetcher.fetch_historical_data(
        symbol="BTCUSDT",
        interval="1h",
        start_time=start_time,
        end_time=end_time
    )

    # Verify response
    assert response.success is True
    assert response.data is not None
    assert len(response.data) >= 20  # Should have at least 20 hours of data

    # Verify data is sorted by timestamp
    timestamps = [candle.timestamp for candle in response.data]
    assert timestamps == sorted(timestamps)

    # Verify all data is within the requested time range
    for candle in response.data:
        assert start_time <= candle.timestamp <= end_time
        assert candle.symbol == "BTCUSDT"
        assert candle.interval == "1h"

@pytest.mark.asyncio
async def test_real_data_validation(binance_fetcher):
    """Test OHLCV data validation using real market data."""
    # Fetch real data and verify it passes validation
    response = await binance_fetcher.fetch_klines("BTCUSDT", "1h", limit=1)

    assert response.success is True
    assert len(response.data) == 1

    # Real market data should always pass validation
    real_candle = response.data[0]
    assert isinstance(real_candle, OHLCVSchema)

    # Test that invalid data fails validation
    with pytest.raises(ValueError):
        OHLCVSchema(
            timestamp=real_candle.timestamp,
            open=real_candle.open,
            high=real_candle.open - 1000,  # Invalid: high < open
            low=real_candle.low,
            close=real_candle.close,
            volume=real_candle.volume,
            symbol=real_candle.symbol,
            interval=real_candle.interval
        )
