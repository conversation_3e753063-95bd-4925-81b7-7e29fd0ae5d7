"""
Tests for the Binance data fetcher.
"""
import asyncio
from datetime import datetime, timedelta, timezone
import pytest
from unittest.mock import patch, MagicMock

from src.data.binance_fetcher import BinanceDataFetcher
from src.data.models import OHLCVSchema, DataFetchResponse

@pytest.fixture
def mock_klines_data():
    """Sample klines data for testing."""
    now = int(datetime.now(timezone.utc).timestamp() * 1000)
    return [
        [now - 3600000, "50000.0", "50500.0", "49900.0", "50400.0", "10.5"],
        [now - 7200000, "49500.0", "50000.0", "49400.0", "49900.0", "8.2"],
    ]

@pytest.fixture
def binance_fetcher():
    """Create a BinanceDataFetcher instance for testing."""
    return BinanceDataFetcher()

@pytest.mark.asyncio
async def test_fetch_klines_success(binance_fetcher, mock_klines_data):
    """Test successful klines fetch."""
    with patch.object(binance_fetcher, '_make_request') as mock_request:
        # Setup mock response
        mock_request.return_value = mock_klines_data

        # Make the request
        response = await binance_fetcher.fetch_klines("BTCUSDT", "1h")

        # Verify response
        assert response.success is True
        assert len(response.data) == 2
        assert isinstance(response.data[0], OHLCVSchema)
        assert response.data[0].open == 50000.0
        assert response.data[0].high == 50500.0
        assert response.data[0].low == 49900.0
        assert response.data[0].close == 50400.0
        assert response.data[0].volume == 10.5

@pytest.mark.asyncio
async def test_fetch_historical_data(binance_fetcher, mock_klines_data):
    """Test historical data fetching with pagination."""
    with patch.object(binance_fetcher, 'fetch_klines') as mock_fetch:
        # Setup mock responses for pagination
        first_response = DataFetchResponse(
            success=True,
            data=[
                OHLCVSchema(
                    timestamp=datetime.fromtimestamp(mock_klines_data[0][0] / 1000, tz=timezone.utc),
                    open=float(mock_klines_data[0][1]),
                    high=float(mock_klines_data[0][2]),
                    low=float(mock_klines_data[0][3]),
                    close=float(mock_klines_data[0][4]),
                    volume=float(mock_klines_data[0][5]),
                    symbol="BTCUSDT",
                    interval="1h"
                ),
                OHLCVSchema(
                    timestamp=datetime.fromtimestamp(mock_klines_data[1][0] / 1000, tz=timezone.utc),
                    open=float(mock_klines_data[1][1]),
                    high=float(mock_klines_data[1][2]),
                    low=float(mock_klines_data[1][3]),
                    close=float(mock_klines_data[1][4]),
                    volume=float(mock_klines_data[1][5]),
                    symbol="BTCUSDT",
                    interval="1h"
                )
            ]
        )

        # Second response with no data to end pagination
        second_response = DataFetchResponse(success=True, data=[])

        mock_fetch.side_effect = [first_response, second_response]

        # Make the request
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=3)

        response = await binance_fetcher.fetch_historical_data(
            symbol="BTCUSDT",
            interval="1h",
            start_time=start_time,
            end_time=end_time
        )

        # Verify response
        assert response.success is True
        assert len(response.data) == 2  # 2 from first page
        assert response.data[0].timestamp <= response.data[1].timestamp

@pytest.mark.asyncio
async def test_data_validation():
    """Test OHLCV data validation."""
    # Valid data
    valid_data = {
        "timestamp": datetime.now(timezone.utc),
        "open": 50000.0,
        "high": 50500.0,
        "low": 49500.0,
        "close": 50200.0,
        "volume": 10.5,
        "symbol": "BTCUSDT",
        "interval": "1h"
    }

    # Should not raise
    OHLCVSchema(**valid_data)

    # Invalid data (high < open)
    invalid_data = valid_data.copy()
    invalid_data["high"] = 49000.0

    with pytest.raises(ValueError):
        OHLCVSchema(**invalid_data)
