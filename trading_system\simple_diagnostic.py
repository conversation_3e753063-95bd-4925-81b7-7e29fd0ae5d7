"""
Simple Diagnostic: Why TCN-CNN-PPO is Not Trading
Direct analysis of the existing trained model
"""
import asyncio
import sys
import numpy as np
import torch
from datetime import datetime, timezone
from pathlib import Path
import logging

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.data.binance_fetcher import BinanceDataFetcher
from src.trading.environment import GridTradingEnv, Action
from src.ml.integrated_training import TradingFeatureExtractor, SimpleTCNPPO

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def diagnose_model_behavior():
    """Diagnose why the model is not trading."""
    print("🔍 DIAGNOSING TCN-CNN-PPO MODEL BEHAVIOR")
    print("=" * 50)
    
    # Initialize components
    feature_extractor = TradingFeatureExtractor(lookback_window=24)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Check if trained model exists
    model_path = "models/enhanced_300_model.pth"
    if not Path(model_path).exists():
        print("❌ No trained model found. Running enhanced_300_training.py first...")
        return
    
    # Load trained model
    feature_dim = feature_extractor.get_feature_dim()
    model = SimpleTCNPPO(feature_dim, hidden_dim=64).to(device)
    
    try:
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        print(f"✅ Loaded trained model from {model_path}")
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return
    
    # Get recent market data
    print("\n📊 Fetching recent market data...")
    try:
        async with BinanceDataFetcher() as fetcher:
            response = await fetcher.fetch_klines("BTCUSDT", "1h", limit=50)
        
        if not response.success:
            print("❌ Failed to fetch market data")
            return
        
        candles = response.data
        print(f"✅ Fetched {len(candles)} candles")
        
    except Exception as e:
        print(f"❌ Data fetch error: {e}")
        return
    
    # Analyze model predictions
    print("\n🤖 ANALYZING MODEL PREDICTIONS")
    print("-" * 40)
    
    predictions = []
    action_counts = {0: 0, 1: 0, 2: 0}  # BUY, SELL, HOLD
    action_names = ["BUY", "SELL", "HOLD"]
    
    for i in range(feature_extractor.lookback_window, len(candles)):
        try:
            # Extract features
            features = feature_extractor.extract_features(candles[:i+1])
            state_tensor = torch.FloatTensor(features).unsqueeze(0).to(device)
            
            with torch.no_grad():
                # Get model prediction
                logits, value = model(state_tensor)
                probs = torch.softmax(logits, dim=-1)
                action_idx = torch.argmax(probs, dim=-1).item()
                confidence = torch.max(probs).item()
                
                # Store prediction
                predictions.append({
                    'timestamp': candles[i].timestamp,
                    'price': candles[i].close,
                    'action': action_names[action_idx],
                    'action_idx': action_idx,
                    'confidence': confidence,
                    'buy_prob': probs[0, 0].item(),
                    'sell_prob': probs[0, 1].item(),
                    'hold_prob': probs[0, 2].item(),
                    'value_estimate': value.item()
                })
                
                action_counts[action_idx] += 1
                
        except Exception as e:
            print(f"Error processing candle {i}: {e}")
            continue
    
    # Display results
    total_predictions = len(predictions)
    if total_predictions == 0:
        print("❌ No predictions generated")
        return
    
    print(f"📊 PREDICTION ANALYSIS ({total_predictions} predictions)")
    print("-" * 40)
    
    for action_idx, count in action_counts.items():
        percentage = (count / total_predictions) * 100
        print(f"{action_names[action_idx]}: {count} ({percentage:.1f}%)")
    
    # Show sample predictions
    print(f"\n📋 SAMPLE PREDICTIONS (Last 10)")
    print("-" * 60)
    print(f"{'Time':<20} {'Price':<12} {'Action':<6} {'Conf':<6} {'Value':<8}")
    print("-" * 60)
    
    for pred in predictions[-10:]:
        print(f"{str(pred['timestamp'])[:19]:<20} "
              f"${pred['price']:<11,.2f} "
              f"{pred['action']:<6} "
              f"{pred['confidence']:<5.1%} "
              f"{pred['value_estimate']:<7.2f}")
    
    # Detailed probability analysis
    print(f"\n🎯 PROBABILITY ANALYSIS")
    print("-" * 30)
    
    buy_probs = [p['buy_prob'] for p in predictions]
    sell_probs = [p['sell_prob'] for p in predictions]
    hold_probs = [p['hold_prob'] for p in predictions]
    
    print(f"Average BUY probability: {np.mean(buy_probs):.3f}")
    print(f"Average SELL probability: {np.mean(sell_probs):.3f}")
    print(f"Average HOLD probability: {np.mean(hold_probs):.3f}")
    
    print(f"\nMax BUY probability: {np.max(buy_probs):.3f}")
    print(f"Max SELL probability: {np.max(sell_probs):.3f}")
    print(f"Max HOLD probability: {np.max(hold_probs):.3f}")
    
    # Value estimates analysis
    values = [p['value_estimate'] for p in predictions]
    print(f"\nValue estimates range: {np.min(values):.2f} to {np.max(values):.2f}")
    print(f"Average value estimate: {np.mean(values):.2f}")
    
    # Market condition analysis
    print(f"\n📈 MARKET CONDITIONS DURING ANALYSIS")
    print("-" * 40)
    
    prices = [candle.close for candle in candles]
    price_changes = []
    for i in range(1, len(prices)):
        change = (prices[i] - prices[i-1]) / prices[i-1] * 100
        price_changes.append(change)
    
    print(f"Price range: ${min(prices):,.2f} - ${max(prices):,.2f}")
    print(f"Total price change: {((prices[-1] - prices[0]) / prices[0] * 100):+.2f}%")
    print(f"Average hourly change: {np.mean(price_changes):+.3f}%")
    print(f"Volatility (std): {np.std(price_changes):.3f}%")
    
    # Identify the core issue
    print(f"\n🔍 DIAGNOSIS")
    print("=" * 30)
    
    hold_percentage = (action_counts[2] / total_predictions) * 100
    
    if hold_percentage > 95:
        print("❌ CRITICAL ISSUE: Model is stuck in HOLD mode (>95%)")
        print("🔧 ROOT CAUSE: Extreme risk aversion")
        print("💡 SOLUTION NEEDED:")
        print("   1. Reward function needs major revision")
        print("   2. Add exploration bonuses during training")
        print("   3. Implement curriculum learning")
        print("   4. Use epsilon-greedy exploration")
        
    elif hold_percentage > 80:
        print("⚠️  ISSUE: Model is very conservative (>80% HOLD)")
        print("🔧 ROOT CAUSE: Over-conservative training")
        print("💡 SOLUTION NEEDED:")
        print("   1. Adjust reward shaping")
        print("   2. Increase trading incentives")
        print("   3. Add momentum features")
        
    else:
        print("✅ Model shows reasonable action diversity")
    
    # Check if model ever predicts BUY/SELL with high confidence
    high_conf_trades = [p for p in predictions if p['action'] != 'HOLD' and p['confidence'] > 0.7]
    
    if len(high_conf_trades) == 0:
        print("\n❌ CRITICAL: No high-confidence trading signals")
        print("🔧 Model never feels confident enough to trade")
    else:
        print(f"\n✅ Found {len(high_conf_trades)} high-confidence trading signals")
    
    # Simulation with current model
    print(f"\n🎮 SIMULATION WITH CURRENT MODEL")
    print("-" * 40)
    
    env = GridTradingEnv(initial_balance=300.0)
    env.reset(candles[0].close, candles[0].timestamp)
    
    trades_attempted = 0
    trades_executed = 0
    
    for i, pred in enumerate(predictions):
        if i >= len(candles) - feature_extractor.lookback_window:
            break
            
        candle_idx = i + feature_extractor.lookback_window
        candle = candles[candle_idx]
        
        action_map = {0: Action.BUY, 1: Action.SELL, 2: Action.HOLD}
        action = action_map[pred['action_idx']]
        
        if action != Action.HOLD:
            trades_attempted += 1
        
        prev_positions = len(env.positions)
        env.step(action, candle.close, candle.timestamp, candle)
        
        if len(env.positions) > prev_positions:
            trades_executed += 1
    
    final_balance = env.balance
    total_return = (final_balance / 300.0 - 1) * 100
    
    print(f"Trades attempted: {trades_attempted}")
    print(f"Trades executed: {trades_executed}")
    print(f"Final balance: ${final_balance:.2f}")
    print(f"Total return: {total_return:+.2f}%")
    
    # Final recommendations
    print(f"\n🚀 IMMEDIATE ACTION PLAN")
    print("=" * 30)
    print("1. ✅ Issue identified: Model is too conservative")
    print("2. 🔧 Implement reward shaping to encourage trading")
    print("3. 📊 Add technical indicators (RSI, MACD)")
    print("4. 🎯 Use epsilon-greedy exploration during training")
    print("5. 📈 Implement multi-objective training (return + activity)")
    
    print(f"\n✅ Diagnostic complete! Ready to implement fixes.")


async def main():
    """Main diagnostic function."""
    await diagnose_model_behavior()


if __name__ == "__main__":
    asyncio.run(main())
