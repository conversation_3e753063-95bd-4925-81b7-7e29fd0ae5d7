import torch
import torch.optim as optim
import numpy as np
from typing import List, Tu<PERSON>, Dict, Any, Optional
from collections import deque
import random
import torch.nn.functional as F

from ..models.tcn_cnn_ppo import TCN_CNN_PPO

class PPOBuffer:
    """Buffer for storing and computing returns for PPO."""
    
    def __init__(self, gamma: float = 0.99, gae_lambda: float = 0.95):
        self.states = []
        self.actions = []
        self.log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
        
        self.gamma = gamma
        self.gae_lambda = gae_lambda
    
    def store(
        self,
        state: np.ndarray,
        action: int,
        log_prob: float,
        reward: float,
        value: float,
        done: bool
    ) -> None:
        """Store a single transition."""
        self.states.append(state)
        self.actions.append(action)
        self.log_probs.append(log_prob)
        self.rewards.append(reward)
        self.values.append(value)
        self.dones.append(done)
    
    def compute_returns_and_advantages(
        self, 
        last_value: float, 
        last_done: bool
    ) -> <PERSON><PERSON>[np.ndar<PERSON>, np.ndarray]:
        """Compute returns and advantages using GAE."""
        rewards = np.array(self.rewards)
        values = np.array(self.values + [last_value])
        dones = np.array(self.dones + [last_done])
        
        # Compute TD errors
        deltas = rewards + self.gamma * values[1:] * (1 - dones[1:]) - values[:-1]
        
        # Compute advantages using GAE
        advantages = np.zeros_like(deltas, dtype=np.float32)
        last_gae = 0.0
        for t in reversed(range(len(deltas))):
            if dones[t + 1]:
                last_gae = 0.0
            last_gae = deltas[t] + self.gamma * self.gae_lambda * (1 - dones[t + 1]) * last_gae
            advantages[t] = last_gae
        
        # Compute returns
        returns = advantages + np.array(self.values)
        
        return returns, advantages
    
    def get_batch(self) -> Dict[str, np.ndarray]:
        """Get all stored data as numpy arrays."""
        return {
            'states': np.array(self.states, dtype=np.float32),
            'actions': np.array(self.actions, dtype=np.int64),
            'log_probs': np.array(self.log_probs, dtype=np.float32),
            'rewards': np.array(self.rewards, dtype=np.float32),
            'values': np.array(self.values, dtype=np.float32),
            'dones': np.array(self.dones, dtype=bool)
        }
    
    def clear(self) -> None:
        """Clear the buffer."""
        self.states = []
        self.actions = []
        self.log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
    
    def __len__(self) -> int:
        return len(self.states)


class PPOAgent:
    """Proximal Policy Optimization (PPO) agent with TCN-CNN policy."""
    
    def __init__(
        self,
        obs_dim: int,
        action_dim: int,
        seq_len: int = 16,
        lr: float = 3e-4,
        gamma: float = 0.99,
        gae_lambda: float = 0.95,
        clip_eps: float = 0.2,
        entropy_coef: float = 0.01,
        value_coef: float = 0.5,
        max_grad_norm: float = 0.5,
        device: str = 'cuda' if torch.cuda.is_available() else 'cpu',
        **model_kwargs
    ):
        self.obs_dim = obs_dim
        self.action_dim = action_dim
        self.seq_len = seq_len
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_eps = clip_eps
        self.entropy_coef = entropy_coef
        self.value_coef = value_coef
        self.max_grad_norm = max_grad_norm
        self.device = torch.device(device)
        
        # Initialize policy and optimizer
        self.policy = TCN_CNN_PPO(
            obs_dim=obs_dim,
            action_dim=action_dim,
            **model_kwargs
        ).to(self.device)
        
        self.optimizer = optim.Adam(self.policy.parameters(), lr=lr, eps=1e-5)
        
        # Initialize buffer
        self.buffer = PPOBuffer(gamma, gae_lambda)
    
    def act(
        self, 
        state: np.ndarray, 
        deterministic: bool = False
    ) -> Tuple[int, float, float]:
        """
        Select an action given a state.
        
        Args:
            state: Current state/observation
            deterministic: Whether to use deterministic action selection
            
        Returns:
            action: Selected action
            log_prob: Log probability of the selected action
            value: State value estimate
        """
        with torch.no_grad():
            state_tensor = torch.FloatTensor(state).unsqueeze(0).to(self.device)
            
            if deterministic:
                # Select action with highest probability
                logits, value = self.policy(state_tensor)
                probs = F.softmax(logits, dim=-1)
                action = torch.argmax(probs, dim=-1)
                log_prob = torch.log(probs[0, action.item()] + 1e-10)
            else:
                # Sample action from policy
                action, log_prob, value = self.policy.act(state_tensor)
            
            return action.item(), log_prob.item(), value.item()
    
    def store_transition(
        self,
        state: np.ndarray,
        action: int,
        log_prob: float,
        reward: float,
        value: float,
        done: bool
    ) -> None:
        """Store a single transition in the buffer."""
        self.buffer.store(state, action, log_prob, reward, value, done)
    
    def update(self, next_state: np.ndarray, done: bool, batch_size: int = 64, n_epochs: int = 4) -> Dict[str, float]:
        """
        Update policy using PPO.
        
        Args:
            next_state: Next state (for value bootstrap)
            done: Whether the episode is done
            batch_size: Batch size for updates
            n_epochs: Number of epochs to train
            
        Returns:
            Dictionary of training statistics
        """
        # Compute returns and advantages
        with torch.no_grad():
            next_state_tensor = torch.FloatTensor(next_state).unsqueeze(0).to(self.device)
            _, next_value = self.policy(next_state_tensor)
            next_value = next_value.item()
        
        returns, advantages = self.buffer.compute_returns_and_advantages(next_value, done)
        
        # Convert to tensors
        batch = self.buffer.get_batch()
        states_tensor = torch.FloatTensor(batch['states']).to(self.device)
        actions_tensor = torch.LongTensor(batch['actions']).to(self.device)
        old_log_probs_tensor = torch.FloatTensor(batch['log_probs']).to(self.device)
        returns_tensor = torch.FloatTensor(returns).to(self.device)
        advantages_tensor = torch.FloatTensor(advantages).to(self.device)
        
        # Normalize advantages
        advantages_tensor = (advantages_tensor - advantages_tensor.mean()) / (advantages_tensor.std() + 1e-8)
        
        # Training stats
        stats = {
            'policy_loss': [],
            'value_loss': [],
            'entropy_loss': [],
            'total_loss': [],
            'approx_kl': [],
            'clip_frac': []
        }
        
        # Train for multiple epochs
        for _ in range(n_epochs):
            # Create random indices for minibatch
            indices = np.arange(len(states_tensor))
            np.random.shuffle(indices)
            
            for start in range(0, len(indices), batch_size):
                end = start + batch_size
                batch_indices = indices[start:end]
                
                # Get minibatch
                obs_batch = states_tensor[batch_indices]
                actions_batch = actions_tensor[batch_indices]
                old_log_probs_batch = old_log_probs_tensor[batch_indices]
                returns_batch = returns_tensor[batch_indices]
                advantages_batch = advantages_tensor[batch_indices]
                
                # Get new log probs and values
                logits, values = self.policy(obs_batch)
                probs = F.softmax(logits, dim=-1)
                dist = torch.distributions.Categorical(probs)
                log_probs = dist.log_prob(actions_batch)
                entropy = dist.entropy()
                
                # Calculate policy loss (PPO clip)
                ratio = (log_probs - old_log_probs_batch).exp()
                surr1 = ratio * advantages_batch
                surr2 = torch.clamp(ratio, 1.0 - self.clip_eps, 1.0 + self.clip_eps) * advantages_batch
                policy_loss = -torch.min(surr1, surr2).mean()
                
                # Calculate value loss (MSE with clipped value function)
                values_clipped = values + (values - values.detach()).clamp(-self.clip_eps, self.clip_eps)
                value_loss1 = F.mse_loss(values, returns_batch)
                value_loss2 = F.mse_loss(values_clipped, returns_batch)
                value_loss = torch.max(value_loss1, value_loss2)
                
                # Calculate entropy loss
                entropy_loss = -entropy.mean()
                
                # Total loss
                loss = policy_loss + self.value_coef * value_loss + self.entropy_coef * entropy_loss
                
                # Update model
                self.optimizer.zero_grad()
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.policy.parameters(), self.max_grad_norm)
                self.optimizer.step()
                
                # Log stats
                with torch.no_grad():
                    # Calculate approx KL divergence
                    log_ratio = log_probs - old_log_probs_batch
                    approx_kl = ((log_ratio.exp() - 1) - log_ratio).mean()
                    
                    # Calculate clip fraction
                    clip_frac = (torch.abs(ratio - 1.0) > self.clip_eps).float().mean()
                    
                    stats['policy_loss'].append(policy_loss.item())
                    stats['value_loss'].append(value_loss.item())
                    stats['entropy_loss'].append(entropy_loss.item())
                    stats['total_loss'].append(loss.item())
                    stats['approx_kl'].append(approx_kl.item())
                    stats['clip_frac'].append(clip_frac.item())
        
        # Clear buffer
        self.buffer.clear()
        
        # Return mean of each stat
        return {k: float(np.mean(v)) for k, v in stats.items()}
    
    def save(self, path: str) -> None:
        """Save model weights to file."""
        torch.save({
            'policy_state_dict': self.policy.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
        }, path)
    
    def load(self, path: str) -> None:
        """Load model weights from file."""
        checkpoint = torch.load(path, map_location=self.device)
        self.policy.load_state_dict(checkpoint['policy_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.policy.to(self.device)
    
    def train_mode(self) -> None:
        """Set the policy to training mode."""
        self.policy.train()
    
    def eval_mode(self) -> None:
        """Set the policy to evaluation mode."""
        self.policy.eval()
