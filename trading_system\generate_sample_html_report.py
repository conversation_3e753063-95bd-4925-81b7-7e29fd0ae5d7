"""
Generate Sample HTML Report
Create a sample HTML report to show what the 1000-cycle evaluation will produce
"""
import json
import numpy as np
from datetime import datetime
from pathlib import Path
import sys

# Add src to path
sys.path.append(str(Path(__file__).parent))

from comprehensive_1000_cycle_evaluation import Comprehensive1000CycleEvaluator


def create_sample_analysis_data():
    """Create realistic sample data for demonstration."""
    
    # Generate sample composite scores (realistic distribution)
    np.random.seed(42)  # For reproducible results
    
    # Create realistic composite score distribution
    excellent_scores = np.random.normal(0.85, 0.05, 50)  # 5% excellent
    good_scores = np.random.normal(0.70, 0.08, 200)      # 20% good  
    acceptable_scores = np.random.normal(0.50, 0.10, 400) # 40% acceptable
    poor_scores = np.random.normal(0.25, 0.08, 350)      # 35% poor
    
    composite_scores = np.concatenate([excellent_scores, good_scores, acceptable_scores, poor_scores])
    composite_scores = np.clip(composite_scores, 0, 1)  # Ensure 0-1 range
    
    # Generate correlated returns (higher composite score = better returns)
    returns = []
    for score in composite_scores:
        if score > 0.8:
            ret = np.random.normal(15, 8)  # Excellent: avg +15%
        elif score > 0.6:
            ret = np.random.normal(8, 12)  # Good: avg +8%
        elif score > 0.4:
            ret = np.random.normal(2, 15)  # Acceptable: avg +2%
        else:
            ret = np.random.normal(-5, 18) # Poor: avg -5%
        returns.append(ret)
    
    # Generate trade counts
    trades = np.random.poisson(25, 1000)  # Average 25 trades per 30-day test
    
    # Generate final balances based on returns
    final_balances = [300 * (1 + ret/100) for ret in returns]
    
    # Create composite metrics for each cycle
    composite_metrics_analysis = {
        'win_rate': {
            'mean': 0.52,
            'std': 0.15,
            'min': 0.20,
            'max': 0.85,
            'median': 0.51
        },
        'equity_growth': {
            'mean': 0.08,
            'std': 0.12,
            'min': -0.25,
            'max': 0.35,
            'median': 0.07
        },
        'sortino_ratio': {
            'mean': 1.2,
            'std': 0.8,
            'min': -0.5,
            'max': 3.5,
            'median': 1.1
        },
        'calmar_ratio': {
            'mean': 1.1,
            'std': 0.9,
            'min': -0.3,
            'max': 4.2,
            'median': 0.9
        },
        'profit_factor': {
            'mean': 1.3,
            'std': 0.4,
            'min': 0.6,
            'max': 2.8,
            'median': 1.2
        },
        'max_drawdown': {
            'mean': 0.18,
            'std': 0.08,
            'min': 0.05,
            'max': 0.45,
            'median': 0.16
        },
        'risk_of_ruin': {
            'mean': 0.15,
            'std': 0.12,
            'min': 0.01,
            'max': 0.65,
            'median': 0.12
        },
        'trade_frequency': {
            'mean': 3.2,
            'std': 1.8,
            'min': 0.5,
            'max': 8.5,
            'median': 2.9
        }
    }
    
    # Performance categories
    excellent_count = len([s for s in composite_scores if s >= 0.8])
    good_count = len([s for s in composite_scores if 0.6 <= s < 0.8])
    acceptable_count = len([s for s in composite_scores if 0.4 <= s < 0.6])
    poor_count = len([s for s in composite_scores if s < 0.4])
    
    # Best cycles
    best_indices = np.argsort(composite_scores)[-5:][::-1]  # Top 5
    best_cycles = []
    for i, idx in enumerate(best_indices):
        best_cycles.append({
            'cycle': int(idx),
            'testing': {
                'composite_score': composite_scores[idx],
                'total_return': returns[idx],
                'final_balance': final_balances[idx],
                'total_trades': int(trades[idx])
            }
        })
    
    # Create complete analysis structure
    analysis = {
        'summary': {
            'total_cycles': 1000,
            'valid_cycles': 1000,
            'failed_cycles': 0,
            'success_rate': 100.0
        },
        'composite_scores': {
            'mean': np.mean(composite_scores),
            'std': np.std(composite_scores),
            'min': np.min(composite_scores),
            'max': np.max(composite_scores),
            'median': np.median(composite_scores),
            'percentiles': {
                '25th': np.percentile(composite_scores, 25),
                '75th': np.percentile(composite_scores, 75),
                '90th': np.percentile(composite_scores, 90),
                '95th': np.percentile(composite_scores, 95)
            }
        },
        'returns': {
            'mean': np.mean(returns),
            'std': np.std(returns),
            'min': np.min(returns),
            'max': np.max(returns),
            'median': np.median(returns),
            'positive_cycles': len([r for r in returns if r > 0]),
            'negative_cycles': len([r for r in returns if r < 0])
        },
        'trading_activity': {
            'mean_trades': np.mean(trades),
            'std_trades': np.std(trades),
            'min_trades': np.min(trades),
            'max_trades': np.max(trades)
        },
        'performance_categories': {
            'excellent': {'count': excellent_count, 'percentage': excellent_count/10},
            'good': {'count': good_count, 'percentage': good_count/10},
            'acceptable': {'count': acceptable_count, 'percentage': acceptable_count/10},
            'poor': {'count': poor_count, 'percentage': poor_count/10}
        },
        'composite_metrics_analysis': composite_metrics_analysis,
        'best_cycles': best_cycles,
        'raw_data': {
            'composite_scores': composite_scores.tolist(),
            'returns': returns,
            'trades': trades.tolist(),
            'final_balances': final_balances
        }
    }
    
    return analysis


def main():
    """Generate sample HTML report."""
    print("🎨 GENERATING SAMPLE HTML REPORT")
    print("=" * 50)
    print("📊 Creating realistic sample data...")
    
    # Create sample analysis data
    analysis = create_sample_analysis_data()
    
    print(f"✅ Sample data created:")
    print(f"   Valid cycles: {analysis['summary']['valid_cycles']}")
    print(f"   Average composite score: {analysis['composite_scores']['mean']:.4f}")
    print(f"   Average return: {analysis['returns']['mean']:+.2f}%")
    print(f"   Profitable cycles: {analysis['returns']['positive_cycles']}")
    
    # Initialize evaluator for HTML generation
    evaluator = Comprehensive1000CycleEvaluator()
    
    print(f"\n📄 Generating HTML report...")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    html_report = evaluator.generate_comprehensive_html_report(analysis, timestamp)
    
    # Save sample report
    sample_report_path = "reports/SAMPLE_1000_cycle_report.html"
    Path(sample_report_path).parent.mkdir(parents=True, exist_ok=True)
    
    with open(sample_report_path, 'w', encoding='utf-8') as f:
        f.write(html_report)
    
    print(f"✅ Sample HTML report generated!")
    print(f"📄 Saved to: {sample_report_path}")
    print(f"📊 Report size: {len(html_report):,} characters")
    
    # Also save sample analysis as JSON for reference
    sample_analysis_path = "reports/SAMPLE_1000_cycle_analysis.json"
    with open(sample_analysis_path, 'w') as f:
        json.dump(analysis, f, indent=2, default=str)
    
    print(f"📊 Sample analysis saved to: {sample_analysis_path}")
    
    print(f"\n🎉 SAMPLE REPORT READY!")
    print(f"📄 Open in browser: {sample_report_path}")
    print(f"📊 This shows exactly what the real 1000-cycle evaluation will produce")
    
    # Print summary of what's in the report
    print(f"\n📋 REPORT CONTENTS:")
    print(f"   • Executive summary with key metrics")
    print(f"   • Interactive performance distribution charts")
    print(f"   • Performance category breakdown")
    print(f"   • Composite metrics analysis")
    print(f"   • Best performing cycles table")
    print(f"   • Statistical analysis and correlations")
    print(f"   • Key insights and recommendations")


if __name__ == "__main__":
    main()
