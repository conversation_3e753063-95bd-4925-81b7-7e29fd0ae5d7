"""
Direct Analysis of TCN-CNN-PPO Model Behavior
Analyze the existing trained model to understand why it's not trading
"""
import sys
import numpy as np
import torch
from pathlib import Path
import json

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.ml.integrated_training import TradingFeatureExtractor, SimpleTCNPPO


def analyze_existing_model():
    """Analyze the existing trained model."""
    print("🔍 DIRECT ANALYSIS OF TCN-CNN-PPO MODEL")
    print("=" * 50)

    # Check for trained model
    model_path = "models/enhanced_300_model.pth"
    if not Path(model_path).exists():
        print("❌ No trained model found at models/enhanced_300_model.pth")
        print("🔧 Run enhanced_300_training.py first to train a model")
        return

    # Initialize components
    feature_extractor = TradingFeatureExtractor(lookback_window=24)
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    feature_dim = feature_extractor.get_feature_dim()

    print(f"✅ Feature dimension: {feature_dim}")
    print(f"✅ Device: {device}")

    # Load model
    try:
        model = SimpleTCNPPO(feature_dim, hidden_dim=64).to(device)
        checkpoint = torch.load(model_path, map_location=device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        print(f"✅ Model loaded successfully")

        # Show training history if available
        if 'best_performance' in checkpoint:
            best_perf = checkpoint['best_performance']
            print(f"📊 Training Performance:")
            print(f"   Best Episode: {best_perf['episode']}")
            print(f"   Best Balance: ${best_perf['balance']:.2f}")
            print(f"   Best Return: {best_perf['return']:+.2f}%")

    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return

    # Create synthetic test data to analyze model behavior
    print(f"\n🧪 CREATING SYNTHETIC TEST SCENARIOS")
    print("-" * 40)

    # Generate different market scenarios
    scenarios = create_test_scenarios()

    for scenario_name, features in scenarios.items():
        print(f"\n📊 Testing scenario: {scenario_name}")

        try:
            # Convert to tensor
            state_tensor = torch.FloatTensor(features).unsqueeze(0).to(device)

            with torch.no_grad():
                logits, value = model(state_tensor)
                probs = torch.softmax(logits, dim=-1)
                action_idx = torch.argmax(probs, dim=-1).item()
                confidence = torch.max(probs).item()

                action_names = ["BUY", "SELL", "HOLD"]

                print(f"   Predicted Action: {action_names[action_idx]}")
                print(f"   Confidence: {confidence:.1%}")
                print(f"   BUY prob: {probs[0, 0].item():.3f}")
                print(f"   SELL prob: {probs[0, 1].item():.3f}")
                print(f"   HOLD prob: {probs[0, 2].item():.3f}")
                print(f"   Value estimate: {value.item():.3f}")

        except Exception as e:
            print(f"   ❌ Error: {e}")

    # Analyze model weights
    print(f"\n🔍 MODEL ARCHITECTURE ANALYSIS")
    print("-" * 40)

    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)

    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")

    # Check for potential issues
    print(f"\n🔍 POTENTIAL ISSUES ANALYSIS")
    print("-" * 40)

    issues_found = []

    # Check for dead neurons
    with torch.no_grad():
        test_input = torch.randn(1, feature_dim).to(device)
        activations = []

        # Hook to capture activations
        def hook_fn(module, input, output):
            if hasattr(output, 'data'):
                activations.append(output.data)

        # Register hooks
        hooks = []
        for name, module in model.named_modules():
            if isinstance(module, torch.nn.ReLU):
                hooks.append(module.register_forward_hook(hook_fn))

        # Forward pass
        _ = model(test_input)

        # Remove hooks
        for hook in hooks:
            hook.remove()

        # Analyze activations
        if activations:
            dead_neurons = 0
            total_neurons = 0

            for activation in activations:
                zeros = (activation == 0).sum().item()
                total = activation.numel()
                dead_neurons += zeros
                total_neurons += total

            dead_percentage = (dead_neurons / total_neurons) * 100 if total_neurons > 0 else 0
            print(f"Dead neurons: {dead_percentage:.1f}%")

            if dead_percentage > 50:
                issues_found.append("High percentage of dead neurons (>50%)")

    # Check output distribution
    print(f"\n📊 OUTPUT DISTRIBUTION ANALYSIS")
    print("-" * 40)

    # Test with multiple random inputs
    action_counts = {0: 0, 1: 0, 2: 0}
    confidence_scores = []

    with torch.no_grad():
        for _ in range(100):
            random_input = torch.randn(1, feature_dim).to(device)
            logits, _ = model(random_input)
            probs = torch.softmax(logits, dim=-1)
            action_idx = torch.argmax(probs, dim=-1).item()
            confidence = torch.max(probs).item()

            action_counts[action_idx] += 1
            confidence_scores.append(confidence)

    action_names = ["BUY", "SELL", "HOLD"]
    for action_idx, count in action_counts.items():
        percentage = (count / 100) * 100
        print(f"{action_names[action_idx]}: {count}/100 ({percentage:.0f}%)")

    avg_confidence = np.mean(confidence_scores)
    print(f"Average confidence: {avg_confidence:.3f}")

    # Identify issues
    hold_percentage = (action_counts[2] / 100) * 100
    if hold_percentage > 90:
        issues_found.append("Model heavily biased toward HOLD action (>90%)")

    if avg_confidence > 0.95:
        issues_found.append("Model is overconfident (may indicate overfitting)")
    elif avg_confidence < 0.4:
        issues_found.append("Model has low confidence (may indicate poor training)")

    # Final diagnosis
    print(f"\n🎯 DIAGNOSIS SUMMARY")
    print("=" * 30)

    if issues_found:
        print("❌ ISSUES IDENTIFIED:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
    else:
        print("✅ No obvious architectural issues found")

    print(f"\n🔧 RECOMMENDED SOLUTIONS:")
    print("1. Implement reward shaping to encourage trading")
    print("2. Add exploration mechanisms (epsilon-greedy)")
    print("3. Use curriculum learning (start with easier scenarios)")
    print("4. Add technical indicators to features")
    print("5. Implement multi-objective training")

    # Generate simple HTML report
    generate_simple_report(action_counts, confidence_scores, issues_found)

    print(f"\n✅ Analysis complete!")
    print(f"📄 Simple report saved to: reports/model_analysis.html")


def create_test_scenarios():
    """Create synthetic test scenarios for model analysis."""
    feature_dim = 216  # 24 hours * 9 features per hour

    scenarios = {}

    # Scenario 1: Trending up market
    trending_up = np.random.randn(feature_dim) * 0.1
    # Add upward trend in price features
    for i in range(0, feature_dim, 9):
        trending_up[i:i+4] += 0.5  # Boost OHLC features
    scenarios["Trending Up Market"] = trending_up

    # Scenario 2: Trending down market
    trending_down = np.random.randn(feature_dim) * 0.1
    for i in range(0, feature_dim, 9):
        trending_down[i:i+4] -= 0.5  # Reduce OHLC features
    scenarios["Trending Down Market"] = trending_down

    # Scenario 3: Sideways market
    sideways = np.random.randn(feature_dim) * 0.05
    scenarios["Sideways Market"] = sideways

    # Scenario 4: High volatility
    high_vol = np.random.randn(feature_dim) * 0.3
    scenarios["High Volatility Market"] = high_vol

    # Scenario 5: Low volatility
    low_vol = np.random.randn(feature_dim) * 0.02
    scenarios["Low Volatility Market"] = low_vol

    return scenarios


def generate_simple_report(action_counts, confidence_scores, issues_found):
    """Generate a simple HTML report."""
    Path("reports").mkdir(exist_ok=True)

    action_names = ["BUY", "SELL", "HOLD"]

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>TCN-CNN-PPO Model Analysis Report</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            .header {{ text-align: center; color: #2c3e50; }}
            .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
            .issue {{ background: #f8d7da; padding: 10px; margin: 5px 0; }}
            .metric {{ background: #d4edda; padding: 10px; margin: 5px 0; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🔍 TCN-CNN-PPO Model Analysis Report</h1>
            <p>Generated: {Path('models/enhanced_300_model.pth').stat().st_mtime if Path('models/enhanced_300_model.pth').exists() else 'Unknown'}</p>
        </div>

        <div class="section">
            <h2>📊 Action Distribution (100 Random Inputs)</h2>
            <div class="metric">BUY: {action_counts[0]}% ({action_counts[0]}/100)</div>
            <div class="metric">SELL: {action_counts[1]}% ({action_counts[1]}/100)</div>
            <div class="metric">HOLD: {action_counts[2]}% ({action_counts[2]}/100)</div>
            <div class="metric">Average Confidence: {np.mean(confidence_scores):.3f}</div>
        </div>

        <div class="section">
            <h2>🚨 Issues Identified</h2>
            {chr(10).join([f'<div class="issue">❌ {issue}</div>' for issue in issues_found]) if issues_found else '<div class="metric">✅ No major issues detected</div>'}
        </div>

        <div class="section">
            <h2>🔧 Recommended Actions</h2>
            <ol>
                <li>Implement reward shaping to encourage profitable trading</li>
                <li>Add exploration mechanisms during training</li>
                <li>Use curriculum learning approach</li>
                <li>Enhance features with technical indicators</li>
                <li>Implement multi-objective training</li>
            </ol>
        </div>

        <div class="section">
            <h2>🎯 Next Steps</h2>
            <p>Based on this analysis, the model shows conservative behavior.
            The primary issue is likely in the reward function and training approach
            rather than the model architecture itself.</p>
        </div>
    </body>
    </html>
    """

    with open("reports/model_analysis.html", "w", encoding='utf-8') as f:
        f.write(html_content)


if __name__ == "__main__":
    analyze_existing_model()
