"""
20,000-Cycle Comprehensive Evaluation
60-day training + 30-day out-of-sample testing
Full HTML report with equity curves, drawdown charts, and trade-by-trade analysis
"""
import asyncio
import sys
import json
import logging
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Dict, List
import numpy as np

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from comprehensive_1000_cycle_evaluation import Comprehensive1000CycleEvaluator
from enhanced_html_report_generator import EnhancedHTMLReportGenerator

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class Comprehensive20000CycleEvaluator(Comprehensive1000CycleEvaluator):
    """20,000-cycle evaluation with enhanced reporting for live trading preparation."""

    def __init__(self, initial_capital: float = 300.0):
        super().__init__(initial_capital)
        self.total_cycles = 20000
        self.training_days = 60  # Full 60-day training
        self.test_days = 30      # Full 30-day out-of-sample testing

        # Enhanced tracking for live trading preparation
        self.live_trading_metrics = {
            'stability_score': 0.0,
            'consistency_rating': 'Unknown',
            'risk_assessment': 'Unknown',
            'recommendation': 'Unknown'
        }

        logger.info(f"🚀 INITIALIZED 20,000-CYCLE EVALUATOR FOR LIVE TRADING PREPARATION")
        logger.info(f"📊 Training: {self.training_days} days")
        logger.info(f"📊 Testing: {self.test_days} days")
        logger.info(f"📊 Total cycles: {self.total_cycles:,}")
        logger.info(f"📊 Initial capital: ${self.initial_capital}")

    async def run_comprehensive_20000_evaluation(self) -> List[Dict]:
        """Run the complete 20,000-cycle evaluation with enhanced tracking."""
        logger.info("🚀 STARTING 20,000-CYCLE COMPREHENSIVE EVALUATION")
        logger.info("=" * 80)
        logger.info("📊 Training: 60 days | Testing: 30 days | Live Trading Preparation")
        logger.info("📊 Simple 5% Risk Model | Dynamic Position Sizing | Real Market Data")
        logger.info("=" * 80)

        # Collect data once for all cycles
        data_splits = await self.collect_evaluation_data()

        # Validate data quality
        training_candles = len(data_splits['training'])
        testing_candles = len(data_splits['testing'])
        logger.info(f"📊 Training data: {training_candles} candles ({self.training_days} days)")
        logger.info(f"📊 Testing data: {testing_candles} candles ({self.test_days} days)")

        if training_candles < (self.training_days * 20):  # Minimum 20 candles per day
            logger.warning(f"⚠️ Limited training data: {training_candles} candles")

        # Run 20,000 cycles with progress tracking
        results = []
        start_time = datetime.now()

        # Progress tracking intervals
        progress_intervals = [1000, 2500, 5000, 7500, 10000, 12500, 15000, 17500, 20000]

        for cycle in range(self.total_cycles):
            try:
                cycle_result = await self.run_single_cycle(cycle, data_splits)
                results.append(cycle_result)

                # Progress reporting
                if (cycle + 1) in progress_intervals:
                    elapsed = datetime.now() - start_time
                    avg_time_per_cycle = elapsed.total_seconds() / (cycle + 1)
                    remaining_cycles = self.total_cycles - (cycle + 1)
                    eta = timedelta(seconds=avg_time_per_cycle * remaining_cycles)

                    # Calculate current statistics
                    valid_results = [r for r in results if 'testing' in r and 'composite_score' in r['testing']]
                    if valid_results:
                        avg_score = np.mean([r['testing']['composite_score'] for r in valid_results])
                        avg_return = np.mean([r['testing']['total_return'] for r in valid_results])
                        positive_cycles = len([r for r in valid_results if r['testing']['total_return'] > 0])

                        logger.info(f"🎯 PROGRESS: {cycle + 1:,}/{self.total_cycles:,} cycles completed")
                        logger.info(f"   ⏱️ Elapsed: {elapsed} | ETA: {eta}")
                        logger.info(f"   📊 Avg Score: {avg_score:.4f} | Avg Return: {avg_return:+.2f}%")
                        logger.info(f"   📈 Positive Cycles: {positive_cycles}/{len(valid_results)} ({positive_cycles/len(valid_results)*100:.1f}%)")

                # Memory management - save intermediate results every 5000 cycles
                if (cycle + 1) % 5000 == 0:
                    intermediate_file = f"reports/intermediate_results_{cycle + 1}_cycles.json"
                    Path(intermediate_file).parent.mkdir(parents=True, exist_ok=True)
                    with open(intermediate_file, 'w') as f:
                        json.dump(results, f, indent=2, default=str)
                    logger.info(f"💾 Intermediate results saved: {intermediate_file}")

            except Exception as e:
                logger.error(f"❌ Cycle {cycle} failed: {e}")
                # Continue with next cycle
                continue

        total_time = datetime.now() - start_time
        logger.info(f"✅ 20,000-CYCLE EVALUATION COMPLETED!")
        logger.info(f"⏱️ Total time: {total_time}")
        logger.info(f"📊 Valid cycles: {len([r for r in results if 'testing' in r])}/{self.total_cycles}")

        return results

    def calculate_live_trading_readiness(self, analysis: Dict) -> Dict:
        """Calculate live trading readiness metrics."""
        logger.info("📊 Calculating live trading readiness...")

        if 'error' in analysis:
            return {'error': 'Cannot assess readiness due to analysis errors'}

        # Extract key metrics
        composite_scores = analysis['raw_data']['composite_scores']
        returns = analysis['raw_data']['returns']

        # Calculate stability metrics
        score_std = np.std(composite_scores) if composite_scores else 1.0
        score_mean = np.mean(composite_scores) if composite_scores else 0.0

        return_std = np.std(returns) if returns else 100.0
        return_mean = np.mean(returns) if returns else -50.0

        positive_return_ratio = len([r for r in returns if r > 0]) / len(returns) if returns else 0.0

        # Stability score (0-100, higher is better)
        stability_score = max(0, min(100, (
            (score_mean * 50) +  # Composite score contribution
            (max(0, 50 - score_std * 100)) +  # Stability contribution
            (positive_return_ratio * 30) +  # Consistency contribution
            (max(0, 20 - abs(return_std) / 5))  # Volatility contribution
        )))

        # Consistency rating
        if positive_return_ratio >= 0.6 and score_mean >= 0.6:
            consistency = "Excellent"
        elif positive_return_ratio >= 0.5 and score_mean >= 0.5:
            consistency = "Good"
        elif positive_return_ratio >= 0.4 and score_mean >= 0.4:
            consistency = "Fair"
        else:
            consistency = "Poor"

        # Risk assessment
        if return_std <= 10 and score_std <= 0.2:
            risk_level = "Low"
        elif return_std <= 25 and score_std <= 0.4:
            risk_level = "Moderate"
        else:
            risk_level = "High"

        # Live trading recommendation
        if stability_score >= 70 and consistency in ["Excellent", "Good"] and risk_level in ["Low", "Moderate"]:
            recommendation = "READY FOR LIVE TRADING"
        elif stability_score >= 50 and consistency in ["Good", "Fair"]:
            recommendation = "PROCEED WITH CAUTION"
        else:
            recommendation = "NOT READY - NEEDS IMPROVEMENT"

        readiness_metrics = {
            'stability_score': stability_score,
            'consistency_rating': consistency,
            'risk_assessment': risk_level,
            'recommendation': recommendation,
            'detailed_metrics': {
                'composite_score_mean': score_mean,
                'composite_score_std': score_std,
                'return_mean': return_mean,
                'return_std': return_std,
                'positive_return_ratio': positive_return_ratio,
                'total_cycles_analyzed': len(composite_scores)
            }
        }

        logger.info(f"📊 Live Trading Readiness Assessment:")
        logger.info(f"   🎯 Stability Score: {stability_score:.1f}/100")
        logger.info(f"   📈 Consistency: {consistency}")
        logger.info(f"   ⚠️ Risk Level: {risk_level}")
        logger.info(f"   🚀 Recommendation: {recommendation}")

        return readiness_metrics


async def main():
    """Main 20,000-cycle evaluation function."""
    logger.info("🚀 STARTING 20,000-CYCLE EVALUATION FOR LIVE TRADING PREPARATION")
    logger.info("=" * 80)

    # Initialize evaluator
    evaluator = Comprehensive20000CycleEvaluator(initial_capital=300.0)

    # Run comprehensive evaluation
    logger.info("📊 Starting comprehensive evaluation...")
    results = await evaluator.run_comprehensive_20000_evaluation()

    # Analyze results
    logger.info("📊 Analyzing results...")
    analysis = evaluator.analyze_results(results)

    # Calculate live trading readiness
    readiness = evaluator.calculate_live_trading_readiness(analysis)
    analysis['live_trading_readiness'] = readiness

    # Save results with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Save raw results
    results_file = f"reports/20000_cycle_results_{timestamp}.json"
    Path(results_file).parent.mkdir(parents=True, exist_ok=True)

    logger.info("💾 Saving results...")
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)

    # Save analysis
    analysis_file = f"reports/20000_cycle_analysis_{timestamp}.json"
    with open(analysis_file, 'w') as f:
        json.dump(analysis, f, indent=2, default=str)

    # Generate enhanced HTML report
    logger.info("📄 Generating comprehensive HTML report...")
    report_generator = EnhancedHTMLReportGenerator()
    best_cycles = analysis.get('best_cycles', [])
    html_report = report_generator.generate_enhanced_report(analysis, best_cycles, timestamp)
    html_file = f"reports/20000_cycle_live_trading_report_{timestamp}.html"
    with open(html_file, 'w', encoding='utf-8') as f:
        f.write(html_report)

    # Print comprehensive summary
    print("\n🎉 20,000-CYCLE EVALUATION COMPLETED!")
    print("=" * 80)
    print(f"📊 Results saved to: {results_file}")
    print(f"📊 Analysis saved to: {analysis_file}")
    print(f"📊 HTML Report saved to: {html_file}")

    if 'error' not in analysis:
        print(f"\n📈 COMPREHENSIVE STATISTICS:")
        print(f"   Valid cycles: {analysis['summary']['valid_cycles']:,}/20,000")
        print(f"   Average composite score: {analysis['composite_scores']['mean']:.4f}")
        print(f"   Average return: {analysis['returns']['mean']:+.2f}%")
        print(f"   Positive return cycles: {analysis['returns']['positive_cycles']:,}")
        print(f"   Win rate: {analysis['returns']['positive_cycles']/analysis['summary']['valid_cycles']*100:.1f}%")

        print(f"\n🏆 PERFORMANCE CATEGORIES:")
        for category, data in analysis['performance_categories'].items():
            print(f"   {category.capitalize()}: {data['count']:,} cycles ({data['percentage']:.1f}%)")

        print(f"\n🚀 LIVE TRADING READINESS:")
        readiness = analysis['live_trading_readiness']
        print(f"   🎯 Stability Score: {readiness['stability_score']:.1f}/100")
        print(f"   📈 Consistency: {readiness['consistency_rating']}")
        print(f"   ⚠️ Risk Assessment: {readiness['risk_assessment']}")
        print(f"   🚀 Recommendation: {readiness['recommendation']}")

        print(f"\n📄 COMPREHENSIVE HTML REPORT:")
        print(f"   📊 Equity curves and drawdown charts included")
        print(f"   📋 Trade-by-trade analysis for best cycles")
        print(f"   📈 Full composite metrics breakdown")
        print(f"   🎯 Live trading readiness assessment")
        print(f"   📁 Open: {html_file}")

    logger.info("✅ 20,000-cycle evaluation completed successfully!")

    # Open HTML report in browser
    import webbrowser
    import os
    full_path = os.path.abspath(html_file)
    webbrowser.open(f'file://{full_path}')
    print(f"🌐 Opening HTML report in browser...")


if __name__ == "__main__":
    asyncio.run(main())
