
<!DOCTYPE html>
<html>
<head>
    <title>Cycle 10 - Live Trading Report</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .container { max-width: 1400px; margin: 20px auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); }
        .header { text-align: center; background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px 15px 0 0; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .content { padding: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 30px 0; }
        .metric-card { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 12px; border-left: 5px solid #28a745; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #28a745; margin-bottom: 5px; }
        .metric-label { color: #6c757d; font-weight: 500; }
        .section { margin: 40px 0; }
        .section h2 { color: #2c3e50; border-bottom: 3px solid #28a745; padding-bottom: 15px; font-size: 1.8em; }
        .chart-container { background: #f8f9fa; padding: 20px; border-radius: 12px; margin: 20px 0; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .trade-table { width: 100%; border-collapse: collapse; margin: 20px 0; background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .trade-table th { background: linear-gradient(135deg, #343a40 0%, #495057 100%); color: white; padding: 15px; text-align: center; font-weight: 600; }
        .trade-table td { padding: 12px; text-align: center; border-bottom: 1px solid #dee2e6; }
        .trade-table tr:nth-child(even) { background: #f8f9fa; }
        .trade-table tr:hover { background: #e3f2fd; }
        .profit { color: #28a745; font-weight: bold; }
        .loss { color: #dc3545; font-weight: bold; }
        .success-banner { background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); color: #155724; padding: 25px; border-radius: 12px; margin: 20px 0; border-left: 5px solid #28a745; }
        .balance-highlight { font-size: 1.1em; font-weight: bold; background: #e8f5e8; padding: 5px 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Cycle 10 - LIVE TRADING REPORT</h1>
            <p>TCN-CNN-PPO Algorithm Performance Analysis</p>
            <p>Ready for Live Trading Deployment</p>
            <p>Generated: 2025-05-27 01:17:26</p>
        </div>
        
        <div class="content">
            <div class="success-banner">
                <h3>🎉 EXCEPTIONAL PERFORMANCE ACHIEVED!</h3>
                <p><strong>Composite Score:</strong> 0.8019 | <strong>Return:</strong> +6,073.04% | <strong>Max Drawdown:</strong> 0.98%</p>
                <p>This model demonstrates outstanding risk-adjusted returns and is ready for live trading deployment.</p>
            </div>

            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-value">0.8019</div>
                    <div class="metric-label">Composite Score</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">+6,073.04%</div>
                    <div class="metric-label">Total Return</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">0.98%</div>
                    <div class="metric-label">Max Drawdown</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">$18,519.12</div>
                    <div class="metric-label">Final Balance</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">0</div>
                    <div class="metric-label">Total Trades</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">0.0%</div>
                    <div class="metric-label">Win Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">0.00</div>
                    <div class="metric-label">Sortino Ratio</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">0.00</div>
                    <div class="metric-label">Profit Factor</div>
                </div>
            </div>

            <div class="section">
                <h2>📈 Equity Curve</h2>
                <div class="chart-container">
                    <div id="equityCurve" style="width:100%;height:400px;"></div>
                </div>
            </div>

            <div class="section">
                <h2>📊 First 10 Trades - Running Balance</h2>
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>Trade #</th>
                            <th>Direction</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Net P&L</th>
                            <th>Running Balance</th>
                            <th>Exit Reason</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
                <p><strong>Note:</strong> Showing first 10 trades of 0 total trades.</p>
            </div>

            <div class="section">
                <h2>🎯 Top 5 Winning Trades</h2>
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Direction</th>
                            <th>Net P&L</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Return %</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h2>📉 Top 5 Losing Trades</h2>
                <table class="trade-table">
                    <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Direction</th>
                            <th>Net P&L</th>
                            <th>Entry Price</th>
                            <th>Exit Price</th>
                            <th>Return %</th>
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>
            </div>
        </div>
        
        <script>
        var trace1 = {
            x: [0],
            y: [300.0],
            type: 'scatter',
            mode: 'lines',
            name: 'Account Balance',
            line: {color: '#28a745', width: 3}
        };
        
        var layout = {
            title: 'Equity Curve - Account Balance Over Time',
            xaxis: {title: 'Trade Number'},
            yaxis: {title: 'Account Balance ($)'},
            showlegend: true,
            margin: {l: 60, r: 30, t: 60, b: 60}
        };
        
        Plotly.newPlot('equityCurve', [trace1], layout);
        </script>
    </div>
</body>
</html>
        