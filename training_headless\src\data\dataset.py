"""Dataset classes for cryptocurrency time series data."""

import torch
import numpy as np
import pandas as pd
from typing import <PERSON><PERSON>, Dict, Any, Optional, List, Union
from torch.utils.data import Dataset


class CryptoDataset(Dataset):
    """Dataset for cryptocurrency time series data with sequence generation."""
    
    def __init__(
        self, 
        df: pd.DataFrame,
        seq_len: int = 60,
        transform = None,
        target_transform = None
    ):
        """
        Initialize the dataset.
        
        Args:
            df: DataFrame with OHLCV data and indicators
            seq_len: Length of input sequences
            transform: Optional transform to be applied to the features
            target_transform: Optional transform to be applied to the target
        """
        self.df = df.copy()
        self.seq_len = seq_len
        self.transform = transform
        self.target_transform = target_transform
        
        # Convert to numpy for faster indexing
        self.data = self.df.values.astype(np.float32)
        self.timestamps = self.df.index.values
    
    def __len__(self) -> int:
        """Return the number of sequences in the dataset."""
        return len(self.df) - self.seq_len
    
    def __getitem__(self, idx: int) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:
        """
        Get a sequence and its target.
        
        Args:
            idx: Index of the sequence
            
        Returns:
            tuple: (sequence, target) where target is the next time step's return
        """
        if torch.is_tensor(idx):
            idx = idx.tolist()
        
        # Get sequence of seq_len time steps
        sequence = self.data[idx:idx + self.seq_len]
        
        # Target is the next time step's return (can be modified)
        target = self.data[idx + self.seq_len]
        
        # Apply transforms if specified
        if self.transform:
            sequence = self.transform(sequence)
            
        if self.target_transform:
            target = self.target_transform(target)
        
        return sequence, target
    
    def get_sequence(self, idx: int, seq_len: int = None) -> np.ndarray:
        """
        Get a sequence of data points.
        
        Args:
            idx: Starting index of the sequence
            seq_len: Length of the sequence (defaults to self.seq_len)
            
        Returns:
            np.ndarray: Sequence of data points
        """
        seq_len = seq_len or self.seq_len
        return self.data[idx:idx + seq_len]
    
    def get_timestamps(self, idx: int, seq_len: int = None) -> np.ndarray:
        """
        Get timestamps for a sequence.
        
        Args:
            idx: Starting index of the sequence
            seq_len: Length of the sequence (defaults to self.seq_len)
            
        Returns:
            np.ndarray: Array of timestamps
        """
        seq_len = seq_len or self.seq_len
        return self.timestamps[idx:idx + seq_len]
    
    def get_features(self) -> List[str]:
        """Get the list of feature names."""
        return self.df.columns.tolist()
    
    def get_feature_indices(self, features: List[str]) -> List[int]:
        """
        Get column indices for the specified features.
        
        Args:
            features: List of feature names
            
        Returns:
            List[int]: Column indices
        """
        return [self.df.columns.get_loc(f) for f in features if f in self.df.columns]


class MultiTimeframeDataset(Dataset):
    """Dataset that combines multiple timeframes of the same asset."""
    
    def __init__(
        self, 
        datasets: Dict[str, CryptoDataset],
        seq_len: int = 60,
        transform = None
    ):
        """
        Initialize the multi-timeframe dataset.
        
        Args:
            datasets: Dictionary mapping timeframe names to CryptoDatasets
            seq_len: Length of input sequences
            transform: Optional transform to be applied to the features
        """
        self.datasets = datasets
        self.timeframes = list(datasets.keys())
        self.seq_len = seq_len
        self.transform = transform
        
        # Find common time range across all timeframes
        self._align_timeframes()
    
    def _align_timeframes(self) -> None:
        """Align datasets to common time range."""
        # Get common timestamps across all timeframes
        common_timestamps = None
        
        for tf, dataset in self.datasets.items():
            timestamps = dataset.timestamps
            if common_timestamps is None:
                common_timestamps = set(timestamps)
            else:
                common_timestamps.intersection_update(timestamps)
        
        # Sort timestamps
        self.common_timestamps = sorted(common_timestamps)
        
        # Create index mapping for each timeframe
        self.index_maps = {}
        for tf, dataset in self.datasets.items():
            timestamp_to_idx = {ts: i for i, ts in enumerate(dataset.timestamps)}
            self.index_maps[tf] = [timestamp_to_idx[ts] for ts in self.common_timestamps]
    
    def __len__(self) -> int:
        """Return the number of sequences in the dataset."""
        return len(self.common_timestamps) - self.seq_len
    
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """
        Get a sequence of data points across all timeframes.
        
        Args:
            idx: Index of the sequence
            
        Returns:
            dict: Dictionary mapping timeframe names to sequences
        """
        if torch.is_tensor(idx):
            idx = idx.tolist()
        
        sequences = {}
        
        for tf, dataset in self.datasets.items():
            # Get the corresponding indices for this timeframe
            tf_indices = self.index_maps[tf][idx:idx + self.seq_len]
            
            # Get the sequence from the dataset
            sequence = np.stack([dataset.data[i] for i in tf_indices])
            
            # Apply transform if specified
            if self.transform:
                sequence = self.transform(sequence)
            
            sequences[tf] = sequence
        
        return sequences
