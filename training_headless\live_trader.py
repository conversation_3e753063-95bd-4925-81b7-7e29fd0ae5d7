"""Live trading module for the TCN-CNN-PPO trading system."""

import os
import time
import json
import logging
import argparse
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

import numpy as np
import pandas as pd
import ccxt
import torch
from tqdm import tqdm

from src.models.tcn_cnn_ppo import TCN_CNN_PPO
from src.data.data_loader import CryptoDataLoader
from src.data.transforms import Compose, AddIndicators, Normalize
from src.config import load_config

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("live_trading.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class LiveTrader:
    """Live trading agent that executes trades based on model predictions."""
    
    def __init__(
        self,
        config: Dict[str, Any],
        model: TCN_CNN_PPO,
        exchange: ccxt.Exchange,
        symbol: str,
        timeframe: str,
        initial_balance: float = 10000.0,
        max_position: float = 1.0,
        commission: float = 0.001,
        device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        """Initialize the live trader.
        
        Args:
            config: Configuration dictionary
            model: Trained model
            exchange: CCXT exchange instance
            symbol: Trading pair symbol (e.g., 'BTC/USDT')
            timeframe: OHLCV timeframe (e.g., '1h', '4h', '1d')
            initial_balance: Initial balance in quote currency
            max_position: Maximum position size as fraction of account
            commission: Trading commission rate
            device: Device to run model on
        """
        self.config = config
        self.model = model
        self.exchange = exchange
        self.symbol = symbol
        self.timeframe = timeframe
        self.initial_balance = initial_balance
        self.max_position = max_position
        self.commission = commission
        self.device = device
        
        # Initialize state
        self.current_position = 0.0
        self.balance = initial_balance
        self.equity = [initial_balance]
        self.trades = []
        self.current_candle = None
        self.data_buffer = []
        
        # Load market data
        self.data_loader = CryptoDataLoader(
            data_dir=config["data"]["data_dir"],
            symbols=[symbol],
            timeframe=timeframe,
            indicators=config["data"].get("indicators", [])
        )
        
        # Setup transforms
        self.transforms = Compose([
            AddIndicators(config["data"].get("indicators", [])),
            Normalize(method="standard")
        ])
        
        # Load initial data
        self._load_historical_data()
        
        logger.info(f"LiveTrader initialized for {symbol} on {timeframe} timeframe")
    
    def _load_historical_data(self) -> None:
        """Load historical data for warm-up."""
        # Load enough data for the window size plus some buffer
        days_ago = (self.config["env"]["window_size"] * 3) // 24 + 1
        start_date = (datetime.utcnow() - timedelta(days=days_ago)).strftime("%Y-%m-%d")
        
        logger.info(f"Loading historical data since {start_date}...")
        
        # Load data from exchange
        ohlcv = self.exchange.fetch_ohlcv(
            self.symbol,
            self.timeframe,
            since=int(datetime.strptime(start_date, "%Y-%m-%d").timestamp() * 1000),
            limit=1000
        )
        
        # Convert to DataFrame
        df = pd.DataFrame(
            ohlcv,
            columns=['timestamp', 'open', 'high', 'low', 'close', 'volume']
        )
        df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
        df.set_index('timestamp', inplace=True)
        
        # Apply transforms
        self.data_buffer = self.transforms(df.values.astype(np.float32))
        
        logger.info(f"Loaded {len(self.data_buffer)} historical candles")
    
    def _get_current_price(self) -> float:
        """Get current market price."""
        ticker = self.exchange.fetch_ticker(self.symbol)
        return float(ticker['last'])
    
    def _get_account_balance(self) -> Tuple[float, float]:
        """Get current account balance."""
        balance = self.exchange.fetch_balance()
        base_currency = self.symbol.split('/')[0]
        quote_currency = self.symbol.split('/')[1]
        
        base_balance = balance.get(base_currency, {}).get('free', 0.0)
        quote_balance = balance.get(quote_currency, {}).get('free', 0.0)
        
        return float(base_balance), float(quote_balance)
    
    def _get_position_size(self, price: float) -> float:
        """Calculate position size based on risk parameters."""
        _, quote_balance = self._get_account_balance()
        position_value = quote_balance * self.max_position
        return position_value / price
    
    def _prepare_observation(self) -> np.ndarray:
        """Prepare observation for the model."""
        # Get the most recent window of data
        window_size = self.config["env"]["window_size"]
        if len(self.data_buffer) < window_size:
            raise ValueError(f"Not enough data. Need at least {window_size} candles, got {len(self.data_buffer)}")
        
        obs = self.data_buffer[-window_size:]
        return obs
    
    def _execute_trade(self, action: int) -> None:
        """Execute a trade based on the model's action."""
        price = self._get_current_price()
        base_balance, quote_balance = self._get_account_balance()
        
        # Action mapping: 0=buy, 1=hold, 2=sell
        if action == 0:  # Buy
            if self.current_position > 0:
                return  # Already in a position
                
            # Calculate position size
            position_size = self._get_position_size(price)
            cost = position_size * price * (1 + self.commission)
            
            if cost > quote_balance:
                logger.warning(f"Insufficient balance for buy order. Need {cost:.2f}, have {quote_balance:.2f}")
                return
            
            # Place buy order (simulated for now)
            logger.info(f"Placing BUY order: {position_size:.6f} {self.symbol} @ {price:.2f}")
            self.current_position = position_size
            self.balance -= cost
            
            # Log trade
            self.trades.append({
                'timestamp': datetime.utcnow(),
                'action': 'buy',
                'price': price,
                'size': position_size,
                'cost': cost,
                'balance': self.balance,
                'position': self.current_position
            })
            
        elif action == 2:  # Sell
            if self.current_position <= 0:
                return  # No position to sell
            
            # Calculate proceeds
            proceeds = self.current_position * price * (1 - self.commission)
            
            # Place sell order (simulated for now)
            logger.info(f"Placing SELL order: {self.current_position:.6f} {self.symbol} @ {price:.2f}")
            self.balance += proceeds
            
            # Log trade
            self.trades.append({
                'timestamp': datetime.utcnow(),
                'action': 'sell',
                'price': price,
                'size': self.current_position,
                'proceeds': proceeds,
                'balance': self.balance,
                'position': 0.0
            })
            
            # Update position
            self.current_position = 0.0
    
    def update_market_data(self) -> None:
        """Update market data with the latest candle."""
        # Fetch latest OHLCV data
        ohlcv = self.exchange.fetch_ohlcv(self.symbol, self.timeframe, limit=2)
        
        if len(ohlcv) < 2:
            logger.warning("Not enough data to update")
            return
        
        # Get the most recent completed candle
        latest_candle = ohlcv[-2]  # -1 is the current forming candle
        
        # Check if this is a new candle
        if self.current_candle is not None and latest_candle[0] == self.current_candle[0]:
            return  # Same candle, no update needed
        
        # Update current candle
        self.current_candle = latest_candle
        
        # Convert to numpy array and add to buffer
        candle_data = np.array([
            latest_candle[1],  # open
            latest_candle[2],  # high
            latest_candle[3],  # low
            latest_candle[4],  # close
            latest_candle[5]   # volume
        ], dtype=np.float32).reshape(1, -1)
        
        # Apply transforms
        transformed = self.transforms(candle_data)
        self.data_buffer = np.vstack([self.data_buffer, transformed]) if len(self.data_buffer) > 0 else transformed
        
        # Keep only the most recent data (2x window size for safety)
        max_length = self.config["env"]["window_size"] * 2
        if len(self.data_buffer) > max_length:
            self.data_buffer = self.data_buffer[-max_length:]
        
        logger.debug(f"Updated market data. Buffer size: {len(self.data_buffer)}")
    
    def run(self, paper_trading: bool = True) -> None:
        """Run the live trading loop."""
        logger.info("Starting live trading loop...")
        
        try:
            while True:
                try:
                    # Update market data
                    self.update_market_data()
                    
                    # Prepare observation
                    obs = self._prepare_observation()
                    
                    # Get model prediction
                    with torch.no_grad():
                        action_probs, _ = self.model(
                            torch.FloatTensor(obs).unsqueeze(0).to(self.device)
                        )
                        action = torch.argmax(action_probs).item()
                    
                    # Execute trade
                    if not paper_trading:
                        self._execute_trade(action)
                    
                    # Log status
                    price = self._get_current_price()
                    portfolio_value = self.balance + (self.current_position * price)
                    self.equity.append(portfolio_value)
                    
                    logger.info(
                        f"Action: {['BUY', 'HOLD', 'SELL'][action]} | "
                        f"Price: {price:.2f} | "
                        f"Position: {self.current_position:.6f} | "
                        f"Balance: {self.balance:.2f} | "
                        f"Equity: {portfolio_value:.2f}"
                    )
                    
                    # Wait for next candle
                    time.sleep(self._get_sleep_interval())
                    
                except Exception as e:
                    logger.error(f"Error in trading loop: {str(e)}", exc_info=True)
                    time.sleep(60)  # Wait a minute before retrying
                    
        except KeyboardInterrupt:
            logger.info("Live trading stopped by user")
            
            # Save results
            self.save_results()
    
    def _get_sleep_interval(self) -> float:
        """Calculate sleep interval until next candle."""
        now = datetime.utcnow()
        
        # Calculate next candle time based on timeframe
        if 'h' in self.timeframe:
            hours = int(self.timeframe.replace('h', ''))
            next_candle = (now.replace(minute=0, second=0, microsecond=0) + 
                          timedelta(hours=(now.hour // hours + 1) * hours - now.hour))
        elif 'd' in self.timeframe:
            days = int(self.timeframe.replace('d', ''))
            next_candle = (now.replace(hour=0, minute=0, second=0, microsecond=0) + 
                         timedelta(days=(now.day // days + 1) * days - now.day + 1))
        else:
            next_candle = now + timedelta(minutes=1)  # Default to 1 minute
        
        return max(0, (next_candle - now).total_seconds())
    
    def save_results(self, output_dir: str = "live_results") -> None:
        """Save trading results to disk."""
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Save trades
        if self.trades:
            trades_df = pd.DataFrame(self.trades)
            trades_df.to_csv(output_path / "trades.csv", index=False)
        
        # Save equity curve
        equity_df = pd.DataFrame({
            'timestamp': pd.date_range(start=datetime.utcnow() - timedelta(seconds=len(self.equity)-1), 
                                     periods=len(self.equity), freq='s'),
            'equity': self.equity
        })
        equity_df.to_csv(output_path / "equity.csv", index=False)
        
        # Save summary
        summary = {
            'initial_balance': self.initial_balance,
            'final_balance': self.balance,
            'final_position': self.current_position,
            'current_price': self._get_current_price(),
            'portfolio_value': self.balance + (self.current_position * self._get_current_price()),
            'total_return_pct': (self.equity[-1] / self.initial_balance - 1) * 100,
            'num_trades': len([t for t in self.trades if t['action'] in ['buy', 'sell']]),
            'start_time': self.trades[0]['timestamp'].isoformat() if self.trades else None,
            'end_time': datetime.utcnow().isoformat()
        }
        
        with open(output_path / "summary.json", 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Results saved to {output_path.absolute()}")


def initialize_exchange(config: Dict[str, Any]) -> ccxt.Exchange:
    """Initialize and return a CCXT exchange instance."""
    exchange_id = config["exchange"]["id"]
    api_key = os.getenv("EXCHANGE_API_KEY")
    api_secret = os.getenv("EXCHANGE_API_SECRET")
    
    if not api_key or not api_secret:
        raise ValueError("Exchange API key and secret must be set in environment variables")
    
    # Create exchange instance
    exchange_class = getattr(ccxt, exchange_id)
    exchange = exchange_class({
        'apiKey': api_key,
        'secret': api_secret,
        'enableRateLimit': True,
        'options': {
            'defaultType': 'future' if config["exchange"].get("is_futures", False) else 'spot'
        }
    })
    
    # Set sandbox mode if needed
    if config["exchange"].get("sandbox", False):
        exchange.set_sandbox_mode(True)
    
    # Test connectivity
    try:
        exchange.fetch_balance()
        logger.info(f"Successfully connected to {exchange_id}")
    except Exception as e:
        logger.error(f"Failed to connect to {exchange_id}: {str(e)}")
        raise
    
    return exchange


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Live trading with TCN-CNN-PPO model")
    parser.add_argument("--config", type=str, required=True,
                        help="Path to config file")
    parser.add_argument("--checkpoint", type=str, required=True,
                        help="Path to model checkpoint")
    parser.add_argument("--symbol", type=str, default="BTC/USDT",
                        help="Trading pair symbol")
    parser.add_argument("--timeframe", type=str, default="1h",
                        help="OHLCV timeframe")
    parser.add_argument("--paper", action="store_true",
                        help="Run in paper trading mode (no real trades)")
    parser.add_argument("--output-dir", type=str, default="live_results",
                        help="Output directory for results")
    parser.add_argument("--device", type=str, default=None,
                        help="Device to use (cpu, cuda, cuda:0, etc.)")
    return parser.parse_args()


def main():
    """Main function for live trading."""
    # Parse arguments
    args = parse_args()
    
    # Set device
    device = args.device or ("cuda" if torch.cuda.is_available() else "cpu")
    logger.info(f"Using device: {device}")
    
    # Load config
    config = load_config(args.config)
    
    # Initialize exchange
    try:
        exchange = initialize_exchange(config)
    except Exception as e:
        logger.error(f"Failed to initialize exchange: {str(e)}")
        return
    
    # Load model
    try:
        model = TCN_CNN_PPO(
            obs_dim=config["model"]["obs_dim"],
            action_dim=config["model"]["action_dim"],
            tcn_channels=config["model"]["tcn_channels"],
            cnn_filters=config["model"]["cnn_filters"],
            kernel_size=config["model"]["kernel_size"],
            dropout=0.0  # Disable dropout for inference
        ).to(device)
        
        checkpoint = torch.load(args.checkpoint, map_location=device)
        model.load_state_dict(checkpoint["model_state_dict"])
        model.eval()
        
        logger.info(f"Loaded model from {args.checkpoint}")
    except Exception as e:
        logger.error(f"Failed to load model: {str(e)}")
        return
    
    # Initialize live trader
    trader = LiveTrader(
        config=config,
        model=model,
        exchange=exchange,
        symbol=args.symbol,
        timeframe=args.timeframe,
        initial_balance=config["env"].get("initial_balance", 10000.0),
        max_position=config["env"].get("max_position", 1.0),
        commission=config["env"].get("commission", 0.001),
        device=device
    )
    
    # Start trading
    try:
        trader.run(paper_trading=args.paper)
    except KeyboardInterrupt:
        logger.info("Live trading stopped by user")
    except Exception as e:
        logger.error(f"Error in live trading: {str(e)}", exc_info=True)
    finally:
        # Save results
        trader.save_results(args.output_dir)
        
        # Close exchange connection
        if hasattr(exchange, 'close'):
            exchange.close()


if __name__ == "__main__":
    main()
