"""
Investigate Max Drawdown Issue
Generate detailed reports for specific cycles to analyze the 98.8% max drawdown concern
"""
import asyncio
import sys
import json
import numpy as np
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List

# Fix for Windows event loop issue
if sys.platform == 'win32':
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# Add src to path
sys.path.append(str(Path(__file__).parent))

from comprehensive_1000_cycle_evaluation import Comprehensive1000CycleEvaluator
from enhanced_html_report_generator import EnhancedHTMLReportGenerator


class DrawdownInvestigator:
    """Investigate specific cycles to understand max drawdown calculations."""

    def __init__(self):
        self.evaluator = Comprehensive1000CycleEvaluator(initial_capital=300.0)
        
    async def investigate_specific_cycles(self, cycle_numbers: List[int]) -> Dict:
        """Run specific cycles and generate detailed analysis."""
        print("🔍 INVESTIGATING MAX DRAWDOWN ISSUE")
        print("=" * 60)
        print(f"📊 Analyzing cycles: {cycle_numbers}")
        print(f"🎯 Focus: Understanding 98.8% max drawdown calculation")
        print("=" * 60)
        
        # Collect data
        data_splits = await self.evaluator.collect_evaluation_data()
        
        results = {}
        
        for cycle_num in cycle_numbers:
            print(f"\n🔄 ANALYZING CYCLE {cycle_num}...")
            
            try:
                # Run the specific cycle
                cycle_result = await self.evaluator.run_single_cycle(cycle_num, data_splits)
                
                # Extract detailed information
                cycle_analysis = self.analyze_cycle_details(cycle_result, cycle_num)
                results[f"cycle_{cycle_num}"] = cycle_analysis
                
                print(f"✅ Cycle {cycle_num} analysis complete:")
                print(f"   Composite Score: {cycle_analysis['composite_score']:.4f}")
                print(f"   Total Return: {cycle_analysis['total_return']:+.2f}%")
                print(f"   Max Drawdown: {cycle_analysis['max_drawdown']:.2f}%")
                print(f"   Final Balance: ${cycle_analysis['final_balance']:.2f}")
                print(f"   Total Trades: {cycle_analysis['total_trades']}")
                
            except Exception as e:
                print(f"❌ Error analyzing cycle {cycle_num}: {e}")
                results[f"cycle_{cycle_num}"] = {"error": str(e)}
        
        return results
    
    def analyze_cycle_details(self, cycle_result: Dict, cycle_num: int) -> Dict:
        """Analyze detailed cycle information including drawdown calculation."""
        
        # Extract basic metrics
        testing_data = cycle_result.get('testing', {})
        detailed_trades = cycle_result.get('detailed_trades', [])
        
        analysis = {
            'cycle_number': cycle_num,
            'composite_score': testing_data.get('composite_score', 0),
            'total_return': testing_data.get('total_return', 0),
            'final_balance': testing_data.get('final_balance', 300),
            'total_trades': testing_data.get('total_trades', 0),
            'max_drawdown': testing_data.get('max_drawdown', 0),
            'win_rate': testing_data.get('win_rate', 0),
            'sortino_ratio': testing_data.get('sortino_ratio', 0),
            'calmar_ratio': testing_data.get('calmar_ratio', 0),
            'profit_factor': testing_data.get('profit_factor', 0)
        }
        
        # Analyze equity curve and drawdown calculation
        if detailed_trades:
            equity_curve = self.calculate_equity_curve(detailed_trades)
            drawdown_analysis = self.analyze_drawdown_calculation(equity_curve)
            
            analysis.update({
                'equity_curve_analysis': drawdown_analysis,
                'trade_count': len(detailed_trades),
                'first_5_trades': detailed_trades[:5],
                'last_5_trades': detailed_trades[-5:] if len(detailed_trades) >= 5 else detailed_trades,
                'largest_wins': sorted(detailed_trades, key=lambda x: x.get('pnl_net', 0), reverse=True)[:3],
                'largest_losses': sorted(detailed_trades, key=lambda x: x.get('pnl_net', 0))[:3]
            })
        
        return analysis
    
    def calculate_equity_curve(self, trades: List[Dict]) -> List[Dict]:
        """Calculate detailed equity curve with drawdown analysis."""
        equity_curve = []
        running_balance = 300.0  # Starting balance
        peak_balance = 300.0
        max_drawdown = 0.0
        
        # Add starting point
        equity_curve.append({
            'trade_number': 0,
            'balance': running_balance,
            'peak_balance': peak_balance,
            'drawdown': 0.0,
            'drawdown_pct': 0.0
        })
        
        for i, trade in enumerate(trades, 1):
            # Update balance
            net_pnl = trade.get('pnl_net', 0)
            running_balance += net_pnl
            
            # Update peak
            if running_balance > peak_balance:
                peak_balance = running_balance
            
            # Calculate drawdown
            drawdown = peak_balance - running_balance
            drawdown_pct = (drawdown / peak_balance) * 100 if peak_balance > 0 else 0
            
            # Update max drawdown
            if drawdown_pct > max_drawdown:
                max_drawdown = drawdown_pct
            
            equity_curve.append({
                'trade_number': i,
                'trade_pnl': net_pnl,
                'balance': running_balance,
                'peak_balance': peak_balance,
                'drawdown': drawdown,
                'drawdown_pct': drawdown_pct,
                'is_new_peak': running_balance == peak_balance,
                'max_dd_so_far': max_drawdown
            })
        
        return equity_curve
    
    def analyze_drawdown_calculation(self, equity_curve: List[Dict]) -> Dict:
        """Analyze the drawdown calculation in detail."""
        if not equity_curve:
            return {}
        
        max_drawdown_point = max(equity_curve, key=lambda x: x.get('drawdown_pct', 0))
        final_point = equity_curve[-1]
        
        # Find the peak before max drawdown
        max_dd_trade = max_drawdown_point['trade_number']
        peak_before_max_dd = None
        
        for point in equity_curve:
            if point['trade_number'] <= max_dd_trade and point['is_new_peak']:
                peak_before_max_dd = point
        
        return {
            'starting_balance': equity_curve[0]['balance'],
            'final_balance': final_point['balance'],
            'peak_balance': final_point['peak_balance'],
            'max_drawdown_pct': max_drawdown_point['drawdown_pct'],
            'max_drawdown_trade': max_dd_trade,
            'max_drawdown_balance': max_drawdown_point['balance'],
            'peak_before_max_dd': peak_before_max_dd,
            'total_trades': len(equity_curve) - 1,
            'final_drawdown_pct': final_point['drawdown_pct'],
            'balance_progression': [p['balance'] for p in equity_curve[::10]]  # Every 10th trade
        }

    async def generate_detailed_reports(self, results: Dict):
        """Generate detailed HTML reports for each analyzed cycle."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for cycle_key, cycle_data in results.items():
            if 'error' in cycle_data:
                continue
                
            cycle_num = cycle_data['cycle_number']
            
            # Create detailed HTML report
            html_content = self.generate_cycle_html_report(cycle_data, timestamp)
            
            # Save report
            report_file = f"reports/drawdown_investigation_cycle_{cycle_num}_{timestamp}.html"
            Path(report_file).parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            print(f"📄 Detailed report saved: {report_file}")
        
        # Generate summary report
        summary_html = self.generate_summary_report(results, timestamp)
        summary_file = f"reports/drawdown_investigation_summary_{timestamp}.html"
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_html)
        
        print(f"📄 Summary report saved: {summary_file}")
        
        return summary_file

    def generate_cycle_html_report(self, cycle_data: Dict, timestamp: str) -> str:
        """Generate detailed HTML report for a specific cycle."""
        cycle_num = cycle_data['cycle_number']
        
        # Extract equity curve data for chart
        equity_analysis = cycle_data.get('equity_curve_analysis', {})
        
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Cycle {cycle_num} - Drawdown Investigation</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }}
                .header {{ text-align: center; background: #2c3e50; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
                .metric-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }}
                .metric-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #3498db; }}
                .metric-value {{ font-size: 1.5em; font-weight: bold; color: #2c3e50; }}
                .metric-label {{ color: #666; margin-top: 5px; }}
                .section {{ margin: 30px 0; }}
                .section h2 {{ color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
                .drawdown-analysis {{ background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; }}
                .trade-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .trade-table th, .trade-table td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                .trade-table th {{ background: #f8f9fa; }}
                .profit {{ color: #28a745; }}
                .loss {{ color: #dc3545; }}
                .warning {{ background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔍 Cycle {cycle_num} - Drawdown Investigation</h1>
                    <p>Detailed Analysis of Max Drawdown Calculation</p>
                    <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="metric-grid">
                    <div class="metric-card">
                        <div class="metric-value">{cycle_data['composite_score']:.4f}</div>
                        <div class="metric-label">Composite Score</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{cycle_data['total_return']:+.2f}%</div>
                        <div class="metric-label">Total Return</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{cycle_data['max_drawdown']:.2f}%</div>
                        <div class="metric-label">Max Drawdown</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${cycle_data['final_balance']:.2f}</div>
                        <div class="metric-label">Final Balance</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{cycle_data['total_trades']}</div>
                        <div class="metric-label">Total Trades</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{cycle_data.get('win_rate', 0):.1f}%</div>
                        <div class="metric-label">Win Rate</div>
                    </div>
                </div>
                
                <div class="warning">
                    <h3>⚠️ MAX DRAWDOWN CONCERN</h3>
                    <p>The reported max drawdown of {cycle_data['max_drawdown']:.2f}% seems extremely high for a system starting with $300 and ending with ${cycle_data['final_balance']:.2f}.</p>
                    <p>This suggests there may be an issue with the drawdown calculation method.</p>
                </div>
                
                <div class="section">
                    <h2>📊 Drawdown Analysis</h2>
                    <div class="drawdown-analysis">
                        <h3>Detailed Drawdown Calculation:</h3>
                        <p><strong>Starting Balance:</strong> ${equity_analysis.get('starting_balance', 300):.2f}</p>
                        <p><strong>Peak Balance:</strong> ${equity_analysis.get('peak_balance', 0):.2f}</p>
                        <p><strong>Final Balance:</strong> ${equity_analysis.get('final_balance', 0):.2f}</p>
                        <p><strong>Max Drawdown:</strong> {equity_analysis.get('max_drawdown_pct', 0):.2f}%</p>
                        <p><strong>Max DD at Trade:</strong> {equity_analysis.get('max_drawdown_trade', 0)}</p>
                        <p><strong>Balance at Max DD:</strong> ${equity_analysis.get('max_drawdown_balance', 0):.2f}</p>
                    </div>
                </div>
                
                <div class="section">
                    <h2>📈 First 5 Trades</h2>
                    <table class="trade-table">
                        <thead>
                            <tr>
                                <th>Trade</th>
                                <th>Direction</th>
                                <th>Entry Price</th>
                                <th>Exit Price</th>
                                <th>Net P&L</th>
                                <th>Exit Reason</th>
                            </tr>
                        </thead>
                        <tbody>
        """
        
        # Add first 5 trades
        for i, trade in enumerate(cycle_data.get('first_5_trades', []), 1):
            pnl = trade.get('pnl_net', 0)
            pnl_class = 'profit' if pnl > 0 else 'loss'
            html_content += f"""
                            <tr>
                                <td>{i}</td>
                                <td>{trade.get('direction', 'N/A')}</td>
                                <td>${trade.get('entry_price', 0):.2f}</td>
                                <td>${trade.get('exit_price', 0):.2f}</td>
                                <td class="{pnl_class}">${pnl:+.2f}</td>
                                <td>{trade.get('exit_reason', 'N/A')}</td>
                            </tr>
            """
        
        html_content += """
                        </tbody>
                    </table>
                </div>
                
                <div class="section">
                    <h2>🎯 Largest Wins</h2>
                    <table class="trade-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Direction</th>
                                <th>Net P&L</th>
                                <th>Entry Price</th>
                                <th>Exit Price</th>
                            </tr>
                        </thead>
                        <tbody>
        """
        
        # Add largest wins
        for i, trade in enumerate(cycle_data.get('largest_wins', []), 1):
            html_content += f"""
                            <tr>
                                <td>{i}</td>
                                <td>{trade.get('direction', 'N/A')}</td>
                                <td class="profit">${trade.get('pnl_net', 0):+.2f}</td>
                                <td>${trade.get('entry_price', 0):.2f}</td>
                                <td>${trade.get('exit_price', 0):.2f}</td>
                            </tr>
            """
        
        html_content += """
                        </tbody>
                    </table>
                </div>
                
                <div class="section">
                    <h2>📉 Largest Losses</h2>
                    <table class="trade-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Direction</th>
                                <th>Net P&L</th>
                                <th>Entry Price</th>
                                <th>Exit Price</th>
                            </tr>
                        </thead>
                        <tbody>
        """
        
        # Add largest losses
        for i, trade in enumerate(cycle_data.get('largest_losses', []), 1):
            html_content += f"""
                            <tr>
                                <td>{i}</td>
                                <td>{trade.get('direction', 'N/A')}</td>
                                <td class="loss">${trade.get('pnl_net', 0):+.2f}</td>
                                <td>${trade.get('entry_price', 0):.2f}</td>
                                <td>${trade.get('exit_price', 0):.2f}</td>
                            </tr>
            """
        
        html_content += """
                        </tbody>
                    </table>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_content

    def generate_summary_report(self, results: Dict, timestamp: str) -> str:
        """Generate summary comparison report."""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Drawdown Investigation Summary</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }}
                .header {{ text-align: center; background: #e74c3c; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
                .comparison-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .comparison-table th, .comparison-table td {{ padding: 15px; text-align: center; border: 1px solid #ddd; }}
                .comparison-table th {{ background: #f8f9fa; }}
                .warning {{ background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; margin: 20px 0; }}
                .conclusion {{ background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔍 MAX DRAWDOWN INVESTIGATION SUMMARY</h1>
                    <p>Analysis of Cycles with Suspected Drawdown Calculation Issues</p>
                    <p>Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
                </div>
                
                <div class="warning">
                    <h3>⚠️ CRITICAL FINDING</h3>
                    <p>Multiple cycles showing 98.8% max drawdown despite positive returns suggests a systematic issue with the drawdown calculation method.</p>
                </div>
                
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Cycle</th>
                            <th>Composite Score</th>
                            <th>Total Return</th>
                            <th>Final Balance</th>
                            <th>Max Drawdown</th>
                            <th>Total Trades</th>
                        </tr>
                    </thead>
                    <tbody>
        """
        
        for cycle_key, cycle_data in results.items():
            if 'error' not in cycle_data:
                html_content += f"""
                        <tr>
                            <td>{cycle_data['cycle_number']}</td>
                            <td>{cycle_data['composite_score']:.4f}</td>
                            <td>{cycle_data['total_return']:+.2f}%</td>
                            <td>${cycle_data['final_balance']:.2f}</td>
                            <td>{cycle_data['max_drawdown']:.2f}%</td>
                            <td>{cycle_data['total_trades']}</td>
                        </tr>
                """
        
        html_content += """
                    </tbody>
                </table>
                
                <div class="conclusion">
                    <h3>🎯 RECOMMENDED ACTIONS</h3>
                    <ol>
                        <li><strong>Fix Drawdown Calculation:</strong> The current method appears to be incorrect</li>
                        <li><strong>Verify Simple 5% Risk Model:</strong> Ensure position sizing is working correctly</li>
                        <li><strong>Review Composite Scoring:</strong> High scores despite extreme drawdowns suggest weighting issues</li>
                        <li><strong>Implement Proper Risk Management:</strong> 98.8% drawdown is unacceptable for live trading</li>
                    </ol>
                </div>
            </div>
        </body>
        </html>
        """
        
        return html_content


async def main():
    """Main investigation function."""
    print("🔍 STARTING DRAWDOWN INVESTIGATION")
    print("=" * 60)
    
    # Initialize investigator
    investigator = DrawdownInvestigator()
    
    # Investigate specific cycles
    cycles_to_analyze = [10, 62, 73]  # Cycle 10 (11,252% return), Cycle 62 (0.8185), Cycle 73 (0.8208)
    
    print(f"📊 Analyzing cycles: {cycles_to_analyze}")
    print(f"🎯 Focus: Understanding max drawdown calculation issue")
    
    # Run investigation
    results = await investigator.investigate_specific_cycles(cycles_to_analyze)
    
    # Generate detailed reports
    summary_file = await investigator.generate_detailed_reports(results)
    
    print(f"\n🎉 INVESTIGATION COMPLETE!")
    print(f"📄 Summary report: {summary_file}")
    print(f"📊 Individual cycle reports generated")
    print(f"🔍 Review reports to understand drawdown calculation issue")


if __name__ == "__main__":
    asyncio.run(main())
