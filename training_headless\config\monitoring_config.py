"""
Monitoring Configuration for Grid Trading System
"""
from typing import Dict, Any

# Default monitoring configuration
DEFAULT_CONFIG: Dict[str, Any] = {
    'monitoring': {
        'enabled': True,
        'check_interval': 300,  # seconds
        'alert_on_warning': True,
        'alert_on_error': True,
    },
    'alerting': {
        'enabled': True,
        'email': {
            'enabled': True,
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'use_tls': True,
            'username': '',  # Set in .env
            'password': '',  # Set in .env
            'from': '<EMAIL>',
            'to': ['<EMAIL>'],
        },
        'slack': {
            'enabled': False,
            'webhook_url': '',  # Set in .env
            'channel': '#trading-alerts',
        },
        'telegram': {
            'enabled': False,
            'bot_token': '',  # Set in .env
            'chat_id': '',    # Set in .env
        }
    },
    'database': {
        'path': 'trading_data.db',
        'backup_enabled': True,
        'backup_dir': 'backups',
        'backup_interval': 86400,  # 24 hours
        'max_backups': 7,
    },
    'trading': {
        'max_concurrent_trades': 3,
        'max_daily_loss_pct': 5.0,
        'max_position_size_pct': 10.0,
    },
    'logging': {
        'level': 'INFO',
        'file': 'trading_bot.log',
        'max_size_mb': 100,
        'backup_count': 5,
    }
}

def load_config(env_vars: Dict[str, str]) -> Dict[str, Any]:
    """
    Load configuration from environment variables
    
    Args:
        env_vars: Dictionary of environment variables
        
    Returns:
        Dict with merged configuration
    """
    import copy
    
    # Create a deep copy of the default config
    config = copy.deepcopy(DEFAULT_CONFIG)
    
    # Update from environment variables
    if 'MONITORING_INTERVAL' in env_vars:
        config['monitoring']['check_interval'] = int(env_vars['MONITORING_INTERVAL'])
        
    # Email settings
    if 'SMTP_USERNAME' in env_vars:
        config['alerting']['email']['username'] = env_vars['SMTP_USERNAME']
    if 'SMTP_PASSWORD' in env_vars:
        config['alerting']['email']['password'] = env_vars['SMTP_PASSWORD']
    if 'ALERT_EMAIL' in env_vars:
        config['alerting']['email']['to'] = [env_vars['ALERT_EMAIL']]
    
    # Database settings
    if 'DB_PATH' in env_vars:
        config['database']['path'] = env_vars['DB_PATH']
        
    # Update logging level if specified
    if 'LOG_LEVEL' in env_vars:
        config['logging']['level'] = env_vars['LOG_LEVEL'].upper()
    
    return config
