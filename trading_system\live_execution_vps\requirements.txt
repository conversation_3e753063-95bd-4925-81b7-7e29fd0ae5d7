# Live Trading Engine Requirements for VPS Deployment

# Core ML and Data Processing
torch>=2.0.0
numpy>=1.24.0
pandas>=2.0.0

# Async HTTP and WebSocket
aiohttp>=3.8.0
websockets>=11.0.0

# Binance API
python-binance>=1.0.0
requests>=2.28.0

# Environment and Configuration
python-dotenv>=1.0.0
pydantic>=2.0.0

# Logging and Monitoring
psutil>=5.9.0
schedule>=1.2.0

# System utilities
pathlib2>=2.3.0
asyncio-mqtt>=0.11.0

# Optional: For enhanced monitoring
prometheus-client>=0.16.0
